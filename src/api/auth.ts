import http from '@/utils/http';
import { authConfig } from '@/utils/config';
import type { BaseResponse } from '@/types/common';
import type { GetUserProfileResponse } from '@/types/auth';

/**
 * 获取用户信息
 */
export async function getUserProfile(): Promise<GetUserProfileResponse> {
  try {
    const response = await http.post(authConfig.getUserProfileApiPath);
    return response.data;
  } catch (error) {
    throw error;
  }
}
/**
 * 退出登录
 */
export async function logout(): Promise<BaseResponse> {
  try {
    const response = await http.get(authConfig.logoutApiPath);
    return response.data;
  } catch (error) {
    throw error;
  }
}


