/**
 * 配置转换工具
 * 
 * 此模块提供将集成编辑页面数据结构转换为标准化JSON格式的函数，
 * 以及将标准化JSON格式转换回集成编辑数据结构的函数。
 * 
 * 主要用于：
 * 1. 在保存查询/集成配置时，将编辑页面的数据结构转换为标准JSON格式
 * 2. 在加载查询/集成配置时，将标准JSON格式转换回编辑页面的数据结构
 */

/**
 * 将集成编辑配置转换为标准化JSON格式
 * @param {Object} integrationData - 集成编辑页面的数据对象
 * @returns {Object} - 标准化的JSON配置对象
 */
export function convertToStandardConfig(integrationData: any): any {
  console.log('[configConverter] 输入数据类型:', typeof integrationData);
  console.log('[configConverter] 输入数据键:', Object.keys(integrationData));
  
  // 提取原始数据
  const { 
    queryParams = [], 
    tableConfig = {}, 
    paramValues = {},
    chartTitle = '', 
    chartType = 'bar',
    chartDescription = '',
    chartHeight = 400,
    chartShowLegend = true,
    chartAnimation = true,
    chartXField = '',
    chartYField = ''
  } = integrationData;
  
  console.log('[configConverter] 提取的queryParams长度:', queryParams.length);
  console.log('[configConverter] 提取的tableConfig.columns:', tableConfig.columns ? tableConfig.columns.length : 0);
  
  // 深拷贝避免修改原始数据
  const result: any = {
    meta: {
      database: integrationData.meta?.database || "",
      schema: integrationData.meta?.schema || "",
      table: integrationData.meta?.table || "",
      pageCode: integrationData.meta?.pageCode || "defaultPage",
      apis: integrationData.meta?.apis || {}
    },
    filter: [],
    list: [],
    operation: {
      paginationEnable: true,
      totalEnable: true,
      downloadEnable: true,
      operationColumnFixed: "right",
      batchEnable: false,
      tableActions: [],
      rowActions: [],
      defaultPageSize: 10
    },
    chart: null
  };

  // 转换filter
  if (queryParams && queryParams.length > 0) {
    result.filter = queryParams.map((param: any) => {
      console.log('[configConverter] 处理queryParam:', param);
      
      // 根据param.formType映射到displayType
      const displayTypeMap: Record<string, string> = {
        'input': 'input',
        'textarea': 'textarea',
        'number': 'input-number',
        'password': 'input-password',
        'date': 'date-picker',
        'date-range': 'date-range-picker',
        'datetime': 'datetime-picker',
        'select': 'select',
        'multiselect': 'select',
        'checkbox': 'checkbox',
        'radio': 'radio'
      };

      // 映射字段类型
      const fieldTypeMap: Record<string, string> = {
        'string': 'string',
        'number': 'number',
        'boolean': 'boolean',
        'date': 'date'
      };

      // 创建基本结构
      const filterItem: any = {
        key: param.name,
        label: param.description || param.name,
        fieldType: fieldTypeMap[param.type] || 'string',
        dataFormat: param.format || 'string',
        displayType: displayTypeMap[param.formType] || 'input',
        config: {
          required: !!param.required
        }
      };

      // 设置默认值 - 使用序列化函数确保类型一致性
      if (param.defaultValue !== undefined && param.defaultValue !== null) {
        // 记录序列化前的值
        console.log(`[configConverter] 序列化默认值前 [${param.name}]: `, param.defaultValue, typeof param.defaultValue);
        
        // 序列化默认值
        const serializedValue = param.defaultValue;
        
        // 记录序列化后的值
        console.log(`[configConverter] 序列化默认值后 [${param.name}]: `, serializedValue, typeof serializedValue);
        
        // 任何非null和undefined的值都设置为defaultValue，包括空字符串
        if (serializedValue !== null && serializedValue !== undefined) {
          filterItem.config.defaultValue = serializedValue;
          console.log(`[configConverter] 最终设置默认值 [${param.name}]: `, filterItem.config.defaultValue);
        } else {
          console.log(`[configConverter] 跳过空值，不设置默认值 [${param.name}]`);
        }
      }
      
      // 根据不同表单类型添加特定配置
      if (param.formType === 'select' || param.formType === 'multiselect') {
        if (param.options && Array.isArray(param.options) && param.options.length > 0) {
          // 优先使用param.enumCode作为enumKey（用户配置的实际枚举编码）
          // 如果没有enumCode，才使用param.name + 'Options'作为后备方案
          filterItem.config.enumKey = param.enumCode || (param.name + 'Options');
          console.log('[configConverter] 设置枚举键:', filterItem.config.enumKey, '参数:', param.name);
        }
        
        // 同时检查formType是multiselect或者参数有multiSelect标记
        if (param.formType === 'multiselect' || param.multiSelect === true) {
          filterItem.config.isMultiValue = true;
          console.log('[configConverter] 设置多选支持:', param.name, '原因:', param.formType === 'multiselect' ? 'formType=multiselect' : 'multiSelect=true');
        }
        
        // 添加搜索和模糊匹配配置
        if (param.searchable !== undefined) {
          filterItem.config.isSearchable = !!param.searchable;
        }
        
        if (param.fuzzyMatch !== undefined) {
          filterItem.config.isFuzzyMatch = !!param.fuzzyMatch;
        }
      } else if (param.formType === 'text' || param.formType === 'textarea') {
        // 为文本输入类型添加模糊匹配配置
        if (param.fuzzyMatch !== undefined) {
          filterItem.config.isFuzzyMatch = !!param.fuzzyMatch;
          console.log(`[configConverter] 设置文本输入模糊匹配 [${param.name}]: ${!!param.fuzzyMatch}`);
        }
        
        // 添加多选支持 (以逗号分隔)
        if (param.allowMultiple !== undefined) {
          filterItem.config.isMultiValue = !!param.allowMultiple;
          console.log(`[configConverter] 设置文本框多选支持 [${param.name}]: ${!!param.allowMultiple}, 原因: allowMultiple=${param.allowMultiple}`);
        }
        
        // 添加长度限制配置
        if (param.minLength !== undefined) {
          filterItem.config.minLength = String(param.minLength);
        }
        
        if (param.maxLength !== undefined) {
          filterItem.config.maxLength = String(param.maxLength);
        }
        
        // 添加验证配置
        if (param.validationRegex) {
          filterItem.config.validationRegex = param.validationRegex;
        }
        
        if (param.validationMessage) {
          filterItem.config.validationMessage = param.validationMessage;
        }
      } else if (param.formType === 'number') {
        // 添加数字类型的特殊配置
        if (param.minValue !== undefined) {
          filterItem.config.min = String(param.minValue);
        }
        
        if (param.maxValue !== undefined) {
          filterItem.config.max = String(param.maxValue);
        }
        
        if (param.precision !== undefined) {
          filterItem.config.fixedPoint = String(param.precision);
        }
      } else if (param.formType === 'date-range') {
        // 处理日期区间特定配置
        if (param.maxDateSpan) {
          filterItem.config.maxDaysRange = String(param.maxDateSpan);
        }
        if (param.dateFormat) {
          filterItem.config.dateFormat = param.dateFormat;
        }
        if (param.disablePastDates === true) {
          filterItem.config.disablePastDates = true;
        }
        
        // 设置dataFormat为日期类型
        filterItem.dataFormat = 'date';
        
        console.log('[configConverter] 配置日期区间参数:', filterItem.config);
      }
      
      console.log('[configConverter] 生成的filter项:', filterItem);
      // 添加更详细的配置日志
      console.log('[configConverter] 配置详情:', {
        key: filterItem.key,
        displayType: filterItem.displayType,
        formType: param.formType,
        multiSelect: param.multiSelect,
        configIsMultiValue: filterItem.config.isMultiValue,
        configEnumKey: filterItem.config.enumKey,
        configIsSearchable: filterItem.config.isSearchable
      });
      return filterItem;
    });
  }
  
  // 如果filter为空，使用空数组
  if (!result.filter || result.filter.length === 0) {
    console.log('[configConverter] filter为空，使用空数组');
    result.filter = [];
  }

  // 转换list(表格列)
  if (tableConfig.columns && tableConfig.columns.length > 0) {
    result.list = tableConfig.columns
      .filter((column: any) => column && column.field) // 确保列数据有效
      .map((column: any, index: number) => {
        console.log('[configConverter] 处理表格列:', column);
        
        // 映射显示类型到columnType
        const columnTypeMap: Record<string, string> = {
          'TEXT': 'text',
          'NUMBER': 'text',  // 用于数字的文本显示
          'INTEGER': 'text',  // 用于整数的文本显示
          'DATE': 'date',
          'DATETIME': 'date',
          'BOOLEAN': 'boolean',
          'TAG': 'tag',
          'STATUS': 'status',
          'SENSITIVE': 'sensitive',
          'SENSITIVITY': 'sensitivity',
          'LINK': 'link',
          'IMAGE': 'image',
          'BADGE': 'badge',
          'DECIMAL': 'text',  // 用于小数的文本显示
          'FLOAT': 'text',  // 用于浮点数的文本显示
          'DOUBLE': 'text',  // 用于双精度的文本显示
          'TIMESTAMP': 'date',  // 时间戳类型使用日期显示
          'VARCHAR': 'text',  // 可变长字符串使用文本显示
          'CHAR': 'text'  // 定长字符串使用文本显示
        };

        // 映射字段类型 
        const fieldTypeMap: Record<string, string> = {
          'TEXT': 'string',
          'NUMBER': 'number',
          'INTEGER': 'number',
          'DATE': 'date',
          'DATETIME': 'date',
          'BOOLEAN': 'boolean',
          'VARCHAR': 'string',
          'CHAR': 'string',
          'DECIMAL': 'number',
          'FLOAT': 'number',
          'DOUBLE': 'number',
          'TIMESTAMP': 'date',
          'TIME': 'string'
        };

        // 确定数据格式
        let dataFormat = 'string'; // 默认为字符串格式
        
        // 根据字段类型设置相应的数据格式
        if (column.type === 'NUMBER' || column.type === 'INTEGER') {
          dataFormat = 'int';
        } else if (column.type === 'DECIMAL' || column.type === 'FLOAT' || column.type === 'DOUBLE') {
          dataFormat = 'decimal';
        } else if (column.type === 'DATE') {
          dataFormat = 'date';
        } else if (column.type === 'DATETIME' || column.type === 'TIMESTAMP') {
          dataFormat = 'datetime';
        } else if (column.type === 'BOOLEAN') {
          dataFormat = 'boolean';
        } else if (column.type === 'TEXT' || column.type === 'VARCHAR' || column.type === 'CHAR') {
          dataFormat = 'string';
        } else if (column.format) {
          // 如果有显式指定的格式，使用指定的格式
          dataFormat = column.format.toLowerCase();
        }
        
        // 确保数据格式字段不为空
        if (!dataFormat) {
          dataFormat = 'string';
        }

        // 根据数据格式(format)始终重新计算推荐的columnType
        // 基于format确定推荐的columnType
        const formatToColumnTypeMap: Record<string, string> = {
          'string': 'TEXT',
          'int': 'NUMBER',
          'decimal': 'NUMBER',
          'enum': 'TAG',
          'date': 'DATE',
          'date-time': 'DATE',
          'card': 'SENSITIVITY',
          'mobile': 'SENSITIVITY',
          'uri': 'LINK',
          'email': 'SENSITIVITY',
          'json': 'TEXT',
          'boolean': 'STATUS'
        };
        
        const originalDisplayType = column.displayType;
        
        // 设置displayType为大写格式
        column.displayType = formatToColumnTypeMap[dataFormat] || 'TEXT';
        console.log(`[configConverter] 根据format设置columnType: format=${dataFormat}, columnType=${column.displayType}, 原始displayType=${originalDisplayType}`);

        const listItem: any = {
          isPrimaryKey: index === 0, // 假设第一列为主键
          key: column.field,
          label: column.label || column.field,
          fieldType: fieldTypeMap[column.type] || 'string',
          dataFormat: dataFormat,
          columnType: (formatToColumnTypeMap[dataFormat] || 'TEXT').toLowerCase(),
          config: {}
        };
        
        console.log(`[configConverter] 列项字段值: field=${column.field}, format=${dataFormat}, displayType=${column.displayType}, columnType=${listItem.columnType}`);

        // 添加列配置
        if (column.width) listItem.config.width = column.width;
        
        // 设置列对齐方式，根据数据类型自动选择默认对齐方式
        if (column.align) {
          listItem.config.align = column.align.toLowerCase();
        } else {
          // 如果没有指定对齐方式，根据类型选择默认值
          if (column.type === 'NUMBER' || column.type === 'INTEGER' || column.type === 'DECIMAL' || 
              column.type === 'FLOAT' || column.type === 'DOUBLE') {
            listItem.config.align = 'right';  // 数字右对齐
          } else if (column.type === 'DATE' || column.type === 'DATETIME' || column.type === 'TIMESTAMP') {
            listItem.config.align = 'center';  // 日期居中
          } else if (column.type === 'BOOLEAN') {
            listItem.config.align = 'center';  // 布尔值居中
          } else {
            listItem.config.align = 'left';  // 文本默认左对齐
          }
        }
        
        if (column.sortable) listItem.config.sortable = column.sortable;
        if (column.filterable) listItem.config.filterable = column.filterable;
        if (column.helpText) listItem.config.help = column.helpText;
        
        // 处理column.config对象中的属性
        if (column.config) {
          console.log(`[configConverter] 处理column.config: field=${column.field}, config=`, column.config);
          // 帮助信息
          if (column.config.help) {
            listItem.config.help = column.config.help;
            console.log(`[configConverter] 设置help: ${column.config.help}`);
          }
          // 文本截断
          if (column.config.truncate !== undefined) {
            listItem.config.truncate = column.config.truncate;
            console.log(`[configConverter] 设置truncate: ${column.config.truncate}`);
          }
          // 兼容旧版的ellipsis属性
          else if (column.config.ellipsis !== undefined) {
            listItem.config.truncate = column.config.ellipsis;
            console.log(`[configConverter] 从旧属性ellipsis设置truncate: ${column.config.ellipsis}`);
          }
          // 小数位数
          if (column.config.fixedPoint !== undefined) {
            listItem.config.fixedPoint = column.config.fixedPoint;
            console.log(`[configConverter] 设置fixedPoint: ${column.config.fixedPoint}`);
          }
          // 千位分隔符
          if (column.config.thousandSeparator !== undefined) {
            listItem.config.thousandSeparator = column.config.thousandSeparator;
            console.log(`[configConverter] 设置thousandSeparator: ${column.config.thousandSeparator}`);
          }
          // 确保排序状态被保存
          if (column.config.sortable !== undefined) {
            listItem.config.sortable = column.config.sortable;
            console.log(`[configConverter] 设置sortable: ${column.config.sortable}`);
          }
          // 确保过滤状态被保存
          if (column.config.filterable !== undefined) {
            listItem.config.filterable = column.config.filterable;
            console.log(`[configConverter] 设置filterable: ${column.config.filterable}`);
          }
        }
        
        // 如果列属性在顶级而不是config中，也要保存到config中
        if (column.sortable !== undefined && listItem.config.sortable === undefined) {
          listItem.config.sortable = column.sortable;
          console.log(`[configConverter] 从顶级属性设置sortable: ${column.sortable}`);
        }
        if (column.filterable !== undefined && listItem.config.filterable === undefined) {
          listItem.config.filterable = column.filterable;
          console.log(`[configConverter] 从顶级属性设置filterable: ${column.filterable}`);
        }
        
        // 调试maskType
        console.log(`[configConverter] 检查maskType字段: column.field=${column.field}, column.maskType=${column.maskType}, format=${dataFormat}`);
        
        // 先检查column中是否已有maskType
        if (column.maskType) {
          listItem.config.maskType = column.maskType;
          console.log(`[configConverter] 已设置maskType: ${column.maskType} 到 config 对象`);
        } 
        // 如果没有，根据format自动设置
        else {
          // 定义format到maskType的映射
          const formatToMaskTypeMap: Record<string, string> = {
            'card': 'bankCard',
            'mobile': 'phone',
            'email': 'email',
            'name': 'name',
            'address': 'address',
            'fixPhone': 'fixPhone',
            'CVV': 'CVV',
            'idCard': 'idCard'
          };
          
          // 根据format自动设置maskType
          if (formatToMaskTypeMap[dataFormat]) {
            listItem.config.maskType = formatToMaskTypeMap[dataFormat];
            console.log(`[configConverter] 根据format自动设置maskType: ${formatToMaskTypeMap[dataFormat]} 到 config 对象`);
          }
        }
        
        // 处理枚举相关配置
        if (column.enumCode) {
          listItem.config.enumKey = column.enumCode;
          listItem.config.enumDisplay = column.enumDisplay || 'text';
          
          if (column.defaultEnumValue) {
            listItem.config.defaultEnumValue = column.defaultEnumValue;
          }
          
          // 根据枚举展示方式调整列类型
          if (column.enumDisplay === 'tag') {
            listItem.columnType = 'tag';
          } else if (column.enumDisplay === 'status') {
            listItem.columnType = 'status';
          } else if (column.enumDisplay === 'badge') {
            listItem.columnType = 'badge';
          }
        }
        // 处理未明确定义enumCode但format为enum的情况
        else if (dataFormat === 'enum' || column.format === 'enum') {
          console.log(`[configConverter] 列 ${column.field} 的format为enum但未指定enumCode，设置默认enumKey`);
          const enumKeyName = `${column.field.toUpperCase()}_ENUM`;
          listItem.config.enumKey = enumKeyName;
          listItem.config.enumDisplay = 'text';
        }
        
        // 添加数字格式化支持
        if (column.precision !== undefined) {
          listItem.config.fixedPoint = String(column.precision);
        }
        if (column.useThousandSeparator !== undefined) {
          listItem.config.thousandSeparator = column.useThousandSeparator;
        }
        
        console.log('[configConverter] 生成的列项:', listItem);
        console.log('[configConverter] 生成的列项config对象:', listItem.config);
        return listItem;
      });
  }
  
  // 如果list为空，添加默认项
  if (!result.list || result.list.length === 0) {
    console.log('[configConverter] list为空，添加默认项');
    result.list = [
      {
        isPrimaryKey: true,
        key: "id",
        label: "ID",
        fieldType: "string",
        dataFormat: "string",
        columnType: "text",
        config: {}
      }
    ];
  }

  // 转换operation(操作配置)
  if (tableConfig.pagination) {
    result.operation.paginationEnable = tableConfig.pagination.enabled !== false;
    if (tableConfig.pagination.pageSize) {
      result.operation.defaultPageSize = tableConfig.pagination.pageSize;
    }
    // 添加总条数显示配置转换
    result.operation.totalEnable = tableConfig.pagination.showTotal !== false;
  }
  
  if (tableConfig.export) {
    result.operation.downloadEnable = tableConfig.export.enabled !== false;
  }
  
  // 处理操作列固定位置
  if (tableConfig.rowActionsFixed !== undefined) {
    result.operation.operationColumnFixed = tableConfig.rowActionsFixed;
  }
  
  // 转换行操作按钮
  if (tableConfig.rowActions && tableConfig.rowActions.length > 0) {
    result.operation.rowActions = tableConfig.rowActions.map((action: any) => {
      const actionItem: any = {
        name: action.label,
        hybridEvent: action.handler || action.action,
        icon: action.icon,
        disabled: action.disabled || !action.enabled
      };
      
      // 添加确认对话框支持
      if (action.confirmationRequired) {
        actionItem.confirm = {
          title: action.confirmationTitle || '确认操作',
          message: action.confirmationMessage || '确定要执行此操作吗？'
        };
      }
      
      // 添加权限控制支持
      if (action.permissions && Array.isArray(action.permissions) && action.permissions.length > 0) {
        actionItem.permissions = action.permissions;
      }
      
      console.log('[configConverter] 生成的行操作按钮:', actionItem);
      return actionItem;
    });
  }
  
  // 转换表格顶部操作按钮
  if (tableConfig.actions && tableConfig.actions.length > 0) {
    result.operation.tableActions = tableConfig.actions.map((action: any) => {
      const actionItem: any = {
        name: action.label,
        hybridEvent: action.handler || action.type,
        icon: action.icon,
        disabled: action.disabled || false
      };
      
      // 添加确认对话框支持
      if (action.confirmationRequired) {
        actionItem.confirm = {
          title: action.confirmationTitle || '确认操作',
          message: action.confirmationMessage || '确定要执行此操作吗？'
        };
      }
      
      // 添加权限控制支持
      if (action.permissions && Array.isArray(action.permissions) && action.permissions.length > 0) {
        actionItem.permissions = action.permissions;
      }
      
      console.log('[configConverter] 生成的操作按钮:', actionItem);
      return actionItem;
    });
  }
  
  // 转换批量操作按钮
  if (tableConfig.batchActions && tableConfig.batchActions.length > 0) {
    result.operation.batchEnable = true;
    // 不再创建单独的batchActions数组，而是将批量操作添加到tableActions中
    if (!result.operation.tableActions) {
      result.operation.tableActions = [];
    }

    // 将批量操作添加到tableActions
    for (const action of tableConfig.batchActions) {
      const batchAction: any = {
        name: action.label,
        hybridEvent: action.handler || action.action,
        icon: action.icon,
        style: action.style || 'default',
        minSelected: action.minSelected || 1,
        disabled: action.disabled === true || action.enabled === false,
        isBatchAction: true // 添加标志表明这是批量操作
      };
      
      // 添加确认对话框支持
      if (action.confirmationRequired) {
        batchAction.confirm = {
          title: action.confirmationTitle || '确认批量操作',
          message: action.confirmationMessage || '确定要执行此批量操作吗？'
        };
      }
      
      // 添加权限控制支持
      if (action.permissions && Array.isArray(action.permissions) && action.permissions.length > 0) {
        batchAction.permissions = action.permissions;
      }
      
      console.log('[configConverter] 生成的批量操作按钮并添加到tableActions:', batchAction);
      result.operation.tableActions.push(batchAction);
    }
  } else {
    result.operation.batchEnable = false;
  }
  
  // 删除不符合官方格式的batchActions字段
  if (result.operation.batchActions) {
    delete result.operation.batchActions;
  }
  
  // 转换chart(图表配置)
  if (integrationData.type === 'CHART' && integrationData.chartConfig) {
    const chartConfig: any = {
      type: chartType,
      title: chartTitle,
      description: chartDescription,
      height: chartHeight,
      showLegend: chartShowLegend,
      animation: chartAnimation,
      xField: chartXField,
      yField: chartYField,
      config: {
        theme: integrationData.chartTheme || 'default',
      }
    };
    
    // 根据图表类型添加特定配置
    if (chartType === 'bar') {
      chartConfig.config.isStack = false;
      chartConfig.config.isGroup = false;
    } else if (chartType === 'pie') {
      chartConfig.nameField = integrationData.chartNameField || '';
      chartConfig.valueField = integrationData.chartValueField || '';
    }
    
    // 将chartConfig赋值给result.chart
    result.chart = chartConfig;
    console.log('[configConverter] 生成的图表配置:', result.chart);
  }
  
  console.log('[configConverter] 最终输出配置的filter数量:', result.filter.length);
  console.log('[configConverter] 最终输出配置的list数量:', result.list.length);
  console.log('[configConverter] 最终输出配置:', result);
  return result;
}

/**
 * 将标准配置转换回集成编辑格式(用于编辑已有配置)
 * @param {Object} standardConfig - 标准化的JSON配置
 * @returns {Object} - 适用于集成编辑页面的数据结构
 */
export function convertFromStandardConfig(standardConfig: any): any {
  // 确保保留原始的meta对象
  const result: any = {
    meta: {
      ...(standardConfig.meta || {}),
      // 确保包含apis字段
      apis: standardConfig.meta?.apis || {}
    },
    queryParams: [],
    tableConfig: {
      columns: [],
      pagination: {
        enabled: true,
        pageSize: 10
      },
      export: {
        enabled: true,
        formats: ['CSV', 'EXCEL']
      },
      actions: [],
      rowActions: [],
      batchActions: []
    },
    paramValues: {},
    chartTitle: '',
    chartDescription: '',
    chartType: 'bar',
    chartTheme: 'default',
    chartHeight: 400,
    chartShowLegend: true,
    chartAnimation: true,
    chartXField: '',
    chartYField: '',
    chartNameField: '',
    chartValueField: ''
  };
  
  // 从标准配置转换filter到queryParams
  if (standardConfig.filter && Array.isArray(standardConfig.filter)) {
    const displayTypeMap: Record<string, string> = {
      'input': 'text',
      'textarea': 'textarea',
      'input-number': 'number',
      'input-password': 'password',
      'date-picker': 'date',
      'date-range-picker': 'date-range',
      'select': 'select'
    };
    
    const fieldTypeMap: Record<string, string> = {
      'string': 'string',
      'number': 'number',
      'boolean': 'boolean',
      'date': 'date'
    };
    
    result.queryParams = standardConfig.filter.map((item: any, index: number) => {
      const param: any = {
        name: item.key,
        type: fieldTypeMap[item.fieldType] || 'string',
        format: item.dataFormat || 'string',
        formType: displayTypeMap[item.displayType] || 'text',
        required: item.config?.required === true,
        description: item.label || item.key,
        displayOrder: index,
        isNewParam: false,
        label: item.label,
        placeholder: item.config?.placeholder
      };
      
      // 设置默认值
      if (item.config?.defaultValue !== undefined && item.config?.defaultValue !== null) {
        param.defaultValue = item.config.defaultValue;
        result.paramValues[item.key] = item.config.defaultValue;
      }
      
      // 处理特定类型配置
      if (item.displayType === 'select') {
        param.multiSelect = item.config?.isMultiValue === true;
        param.searchable = item.config?.isSearchable === true;
        param.fuzzyMatch = item.config?.isFuzzyMatch === true;
        // 可能还需要处理options
      } else if (item.displayType === 'input' || item.displayType === 'textarea') {
        param.fuzzyMatch = item.config?.isFuzzyMatch === true;
        param.allowMultiple = item.config?.isMultiValue === true;
        
        if (item.config?.minLength) param.minLength = parseInt(item.config.minLength);
        if (item.config?.maxLength) param.maxLength = parseInt(item.config.maxLength);
        
        if (item.config?.validationRegex) param.validationRegex = item.config.validationRegex;
        if (item.config?.validationMessage) param.validationMessage = item.config.validationMessage;
      } else if (item.displayType === 'date-range-picker') {
        if (item.config?.maxDaysRange) param.maxDateSpan = parseInt(item.config.maxDaysRange);
        param.dateFormat = item.config?.dateFormat;
      } else if (item.displayType === 'input-number') {
        if (item.config?.min) param.minValue = parseInt(item.config.min);
        if (item.config?.max) param.maxValue = parseInt(item.config.max);
        if (item.config?.fixedPoint) param.precision = parseInt(item.config.fixedPoint);
      }
      
      return param;
    });
  }
  
  // 转换list到tableConfig.columns
  if (standardConfig.list && Array.isArray(standardConfig.list)) {
    const fieldTypeMap: Record<string, string> = {
      'string': 'TEXT',
      'number': 'NUMBER',
      'boolean': 'BOOLEAN',
      'date': 'DATE',
      'integer': 'INTEGER',
      'decimal': 'DECIMAL',
      'float': 'FLOAT',
      'double': 'DOUBLE',
      'datetime': 'DATETIME',
      'timestamp': 'TIMESTAMP'
    };
    
    const columnTypeMap: Record<string, string> = {
      'text': 'TEXT',
      'date': 'DATE',
      'boolean': 'BOOLEAN',
      'select': 'SELECT',
      'image': 'IMAGE',
      'link': 'LINK',
      'button': 'BUTTON',
      'tag': 'TAG',
      'status': 'STATUS',
      'badge': 'BADGE',
      'sensitive': 'SENSITIVE',
      'sensitivity': 'SENSITIVITY',
      'datetime': 'DATETIME',
      'timestamp': 'TIMESTAMP',
      'number': 'NUMBER'
    };
    
    result.tableConfig.columns = standardConfig.list.map((item: any) => {
      const column: any = {
        field: item.key,
        title: item.label,
        dataType: fieldTypeMap[item.fieldType] || 'TEXT',
        format: item.dataFormat || 'string', // 确保数据格式不为空
        type: columnTypeMap[item.columnType] || 'TEXT',
        isPrimary: item.isPrimaryKey === true,
        sortable: true,
        filterable: true,
        visible: true
      };
      
      // 确保数据格式与字段类型匹配
      if (column.dataType === 'NUMBER' && (!column.format || column.format === 'string')) {
        column.format = 'int';
      } else if (column.dataType === 'DATE' && (!column.format || column.format === 'string')) {
        column.format = 'date';
      } else if (column.dataType === 'BOOLEAN' && (!column.format || column.format === 'string')) {
        column.format = 'boolean';
      }
      
      // 处理列配置
      if (item.config) {
        if (item.config.width) column.width = parseInt(item.config.width);
        if (item.config.align) column.align = item.config.align.toUpperCase();
        if (item.config.help) column.helpText = item.config.help;
        if (item.config.sortable !== undefined) column.sortable = item.config.sortable;
        if (item.config.filterable !== undefined) column.filterable = item.config.filterable;
      
        // 初始化 config 对象，确保它存在
        if (!column.config) column.config = {};
      
        // 复制所有配置属性
        if (item.config.fixedPoint !== undefined) column.config.fixedPoint = item.config.fixedPoint;
        if (item.config.thousandSeparator !== undefined) column.config.thousandSeparator = item.config.thousandSeparator;
        if (item.config.showTooltip) column.tooltip = true;
        // 兼容旧版本的ellipsis属性，优先使用truncate
        if (item.config.truncate !== undefined) {
          column.config.truncate = item.config.truncate;
        } else if (item.config.ellipsis !== undefined) {
          column.config.truncate = item.config.ellipsis;
        }
        if (item.config.sortable !== undefined) column.config.sortable = item.config.sortable;
        if (item.config.filterable !== undefined) column.config.filterable = item.config.filterable;
        if (item.config.maskType) column.maskType = item.config.maskType;
        
        // 添加日志记录检查属性设置情况
        console.log(`[列配置同步] 列 ${column.field} 的配置属性:`, { 
          fixedPoint: column.config.fixedPoint,
          thousandSeparator: column.config.thousandSeparator,
          truncate: column.config.truncate,
          sortable: column.config.sortable,
          filterable: column.config.filterable,
          maskType: column.maskType
        });
        
        // 恢复枚举相关配置
        if (item.config.enumKey) {
          column.enumCode = item.config.enumKey;
          column.enumDisplay = item.config.enumDisplay || 'text';
          
          if (item.config.defaultEnumValue) {
            column.defaultEnumValue = item.config.defaultEnumValue;
          }
        }
      } else {
        // 如果没有配置，还要根据字段类型设置默认对齐方式
        const fieldType = fieldTypeMap[item.fieldType] || 'TEXT';
        if (fieldType === 'NUMBER' || fieldType === 'INTEGER' || fieldType === 'DECIMAL' || 
            fieldType === 'FLOAT' || fieldType === 'DOUBLE') {
          column.align = 'RIGHT';
        } else if (fieldType === 'DATE' || fieldType === 'DATETIME' || fieldType === 'TIMESTAMP') {
          column.align = 'CENTER';
        } else if (fieldType === 'BOOLEAN') {
          column.align = 'CENTER';
        } else {
          column.align = 'LEFT';
        }
      }
      
      return column;
    });
  }
  
  // 转换operation配置
  if (standardConfig.operation) {
    // 分页配置
    if (result.tableConfig.pagination) {
      result.tableConfig.pagination.enabled = standardConfig.operation.paginationEnable !== false;
      if (standardConfig.operation.defaultPageSize) {
        result.tableConfig.pagination.pageSize = standardConfig.operation.defaultPageSize;
      }
      // 添加总条数显示配置转换
      result.tableConfig.pagination.showTotal = standardConfig.operation.totalEnable !== false;
    }
    
    // 导出配置
    if (result.tableConfig.export) {
      result.tableConfig.export.enabled = standardConfig.operation.downloadEnable !== false;
    }

    // 处理操作列固定位置
    if (standardConfig.operation.operationColumnFixed !== undefined) {
      result.tableConfig.rowActionsFixed = standardConfig.operation.operationColumnFixed;
    }
    
    // 表格操作按钮
    if (standardConfig.operation.tableActions && Array.isArray(standardConfig.operation.tableActions)) {
      // 将普通操作和批量操作分开
      const normalActions = [];
      const batchActions = [];
      
      for (const action of standardConfig.operation.tableActions) {
        // 如果有isBatchAction标志或有minSelected属性，认为是批量操作
        if (action.isBatchAction || action.minSelected) {
          const batchAction: any = {
            id: action.id || `batch_${Date.now()}`,
            label: action.name,
            action: action.hybridEvent,
            handler: action.hybridEvent,
            icon: action.icon,
            style: action.style || 'default',
            minSelected: action.minSelected || 1,
            enabled: !action.disabled,
            disabled: action.disabled === true
          };
          
          // 添加确认对话框支持
          if (action.confirm) {
            batchAction.confirmationRequired = true;
            batchAction.confirmationTitle = action.confirm.title;
            batchAction.confirmationMessage = action.confirm.message;
          }
          
          // 添加权限控制支持
          if (action.permissions && Array.isArray(action.permissions)) {
            batchAction.permissions = [...action.permissions];
          }
          
          batchActions.push(batchAction);
        } else {
          const actionData: any = {
            label: action.name,
            action: action.hybridEvent,
            handler: action.hybridEvent,
            icon: action.icon,
            enabled: !action.disabled,
            disabled: action.disabled === true,
            type: 'header'
          };
          
          // 添加确认对话框支持
          if (action.confirm) {
            actionData.confirmationRequired = true;
            actionData.confirmationTitle = action.confirm.title;
            actionData.confirmationMessage = action.confirm.message;
          }
          
          // 添加权限控制支持
          if (action.permissions && Array.isArray(action.permissions)) {
            actionData.permissions = [...action.permissions];
          }
          
          normalActions.push(actionData);
        }
      }
      
      // 设置普通表格操作
      result.tableConfig.actions = normalActions;
      
      // 设置批量操作
      result.tableConfig.batchActions = batchActions;
    }
    
    // 行操作按钮
    if (standardConfig.operation.rowActions && Array.isArray(standardConfig.operation.rowActions)) {
      result.tableConfig.rowActions = standardConfig.operation.rowActions.map((action: any) => {
        const actionData: any = {
          label: action.name,
          action: action.hybridEvent,
          handler: action.hybridEvent,
          icon: action.icon,
          enabled: !action.disabled,
          disabled: action.disabled === true,
          type: 'row'
        };
        
        // 添加确认对话框支持
        if (action.confirm) {
          actionData.confirmationRequired = true;
          actionData.confirmationTitle = action.confirm.title;
          actionData.confirmationMessage = action.confirm.message;
        }
        
        // 添加权限控制支持
        if (action.permissions && Array.isArray(action.permissions)) {
          actionData.permissions = [...action.permissions];
        }
        
        return actionData;
      });
    }
    
    // 批量操作设置
    if (standardConfig.operation.batchEnable) {
      if (standardConfig.operation.batchActions && Array.isArray(standardConfig.operation.batchActions) && standardConfig.operation.batchActions.length > 0) {
        // 使用配置中定义的批量操作
        result.tableConfig.batchActions = standardConfig.operation.batchActions.map((action: any) => {
          const actionData: any = {
            id: action.id || `batch_${Date.now()}`,
            label: action.name,
            action: action.hybridEvent,
            handler: action.hybridEvent,
            icon: action.icon,
            style: action.style || 'default',
            minSelected: action.minSelected || 1,
            enabled: !action.disabled,
            disabled: action.disabled === true
          };
          
          // 添加确认对话框支持
          if (action.confirm) {
            actionData.confirmationRequired = true;
            actionData.confirmationTitle = action.confirm.title;
            actionData.confirmationMessage = action.confirm.message;
          }
          
          // 添加权限控制支持
          if (action.permissions && Array.isArray(action.permissions)) {
            actionData.permissions = [...action.permissions];
          }
          
          return actionData;
        });
      } else {
        // 如果没有详细配置但batchEnable为true，仍然添加默认批量操作
        result.tableConfig.batchActions = [
          { 
            id: `batch_export_${Date.now()}`,
            label: '批量导出', 
            action: 'exportSelected',
            handler: 'exportSelected',
            icon: 'download', 
            enabled: true 
          }
        ];
      }
    } else {
      result.tableConfig.batchActions = [];
    }
  }
  
  // 转换chart配置
  if (standardConfig.chart) {
    result.chartType = standardConfig.chart.type || 'bar';
    result.chartTitle = standardConfig.chart.title || '';
    result.chartDescription = standardConfig.chart.description || '';
    result.chartHeight = standardConfig.chart.height || 400;
    result.chartShowLegend = standardConfig.chart.showLegend !== false;
    result.chartAnimation = standardConfig.chart.animation !== false;
    result.chartXField = standardConfig.chart.xField || '';
    result.chartYField = standardConfig.chart.yField || '';
    
    if (standardConfig.chart.config) {
      result.chartTheme = standardConfig.chart.config.theme || 'default';
    }
    
    if (standardConfig.chart.type === 'pie') {
      result.chartNameField = standardConfig.chart.nameField || '';
      result.chartValueField = standardConfig.chart.valueField || '';
    }
  }
  
  return result;
}