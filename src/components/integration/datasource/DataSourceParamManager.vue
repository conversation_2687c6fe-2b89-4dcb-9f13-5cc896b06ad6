<template>
  <div>
    <!-- 数据源参数处理组件 - 无UI -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { message } from '@/services/message';
import { useQueryStore } from '@/stores/query';
import type { QueryParam, IntegrationData } from '@/types/unified-integration';
import type { QueryParametersResponse, QueryParameterObject, QueryField } from '@/types/integration-params';
import {getApiBaseUrl} from "@/services/query";
import instance from '@/utils/axios';

// 定义组件属性
const props = defineProps<{
  integration: IntegrationData;
  queryConditionsPanelRef: any | null;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'params-loaded', params: QueryParam[]): void;
  (e: 'fields-loaded', fields: any[]): void;
  (e: 'param-values-updated', values: Record<string, any>): void;
}>();

// 本地状态
const queryStore = useQueryStore();
const paramValues = ref<Record<string, any>>({});
const isLoading = ref(false);

/**
 * 处理数据源选择
 */
const handleDataSourceSelected = (id: string) => {
  // 由父组件处理，仅作为通信方法
};

/**
 * 从数据源加载条件
 */
const handleLoadFieldsFromDataSource = async () => {
  if (!props.integration.dataSourceId || !props.integration.queryId) {
    message.error('请先选择数据源和查询');
    return;
  }

  // 如果正在加载中，直接返回
  if (isLoading.value) {
    console.log('[DataSourceParamManager] 正在加载中，跳过重复请求');
    return;
  }

  isLoading.value = true;
  try {
    // 加载查询字段
    const fields = await fetchFieldsFromDataSource();
    console.log('[按钮] fetchFieldsFromDataSource 返回:', fields);

    if (!fields || fields.length === 0) {
      message.warning('未从数据源获取到字段');
      return;
    }

    // 使用字段信息生成查询参数
    const params = await loadFieldsAsQueryParams();
    console.log('[按钮] loadFieldsAsQueryParams 返回:', params);

    // 通知父组件加载了字段
    emit('fields-loaded', fields);

    // 如果存在queryConditionsPanel引用，更新其availableFields
    if (props.queryConditionsPanelRef) {
      // 移除setTimeout，改用nextTick
      nextTick(() => {
        try {
          if (props.queryConditionsPanelRef.updateAvailableFields &&
              typeof props.queryConditionsPanelRef.updateAvailableFields === 'function') {
            console.log('[DataSourceParamManager] 更新QueryConditionsPanel的可用字段');
            props.queryConditionsPanelRef.updateAvailableFields(fields);
          }

          // 确保参数列表刷新只执行一次
          if (props.queryConditionsPanelRef.refreshParamsList &&
              typeof props.queryConditionsPanelRef.refreshParamsList === 'function') {
            console.log('[DataSourceParamManager] 刷新参数列表');
            props.queryConditionsPanelRef.refreshParamsList();
          }
        } catch (e) {
          console.error('[DataSourceParamManager] 更新字段或刷新参数列表失败:', e);
        }
      });
    }
  } catch (error) {
    console.error('从数据源加载条件失败:', error);
    message.error('加载条件失败');
  } finally {
    isLoading.value = false;
  }
};

/**
 * 从数据源获取字段列表
 */
const fetchFieldsFromDataSource = async () => {
  if (!props.integration.dataSourceId || !props.integration.queryId) {
    message.error('请先选择数据源和查询');
    return [];
  }

  try {
    message.info('正在从数据源加载字段...');

    // 请求API获取字段数据
    const url = `/api/queries/${props.integration.queryId}/parameters`;
    console.log('[QueryFields] 请求URL:', url);

    const response = await instance.get(url);
    const data = response.data;
    console.log('[QueryFields] 接收到的响应:', data);

    // 增强的数据处理逻辑
    try {
      // 检查数据结构
      if (!data) {
        console.warn('[QueryFields] 响应数据为空');
        return [];
      }

      // 尝试从不同的数据结构中获取 searchFields
      let searchFields;

      if (data.searchFields && Array.isArray(data.searchFields)) {
        // 直接结构
        searchFields = data.searchFields;
      } else if (data.data && data.data.searchFields && Array.isArray(data.data.searchFields)) {
        // 嵌套在 data 字段中
        searchFields = data.data.searchFields;
      } else if (data.fields && Array.isArray(data.fields)) {
        // 尝试使用 fields 字段
        searchFields = data.fields;
      } else if (data.parameters && Array.isArray(data.parameters)) {
        // 尝试使用 parameters 字段
        searchFields = data.parameters;
      }

      if (!searchFields) {
        console.warn('[QueryFields] 未找到有效的字段数据:', data);
        return [];
      }

      // 确保每个字段都有必要的属性
      const processedFields = searchFields.map((field: any) => {
        // 如果字段是字符串，转换为对象
        if (typeof field === 'string') {
          return {
            name: field,
            type: 'string',
            label: field
          };
        }

        // 如果是对象，确保有必要的属性
        return {
          name: field.name || field.fieldName || 'unnamed_field',
          type: field.type || field.dataType || 'string',
          label: field.label || field.name || field.fieldName || 'Unnamed Field',
          tableName: field.tableName || '',  // 保留表名属性
          isEncrypted: field.isEncrypted || false  // 保留加密标志
        };
      });

      console.log('[QueryFields] 处理后的字段:', processedFields);
      return processedFields;

    } catch (e) {
      console.error('[QueryFields] 处理字段数据时出错:', e);
      message.error('处理字段数据时出错');
      return [];
    }
  } catch (error) {
    console.error('[QueryFields] 获取字段失败:', error);
    message.error('获取字段失败');
    return [];
  }
};

/**
 * 将数据类型转换为参数类型
 */
const convertDataTypeToParamType = (type: string) => {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();

  switch (lowerType) {
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
    case 'number':
      return 'number';

    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';

    case 'boolean':
    case 'bool':
      return 'boolean';

    case 'string':
    case 'varchar':
    case 'char':
    case 'text':
    default:
      return 'string';
  }
};

/**
 * 获取表单类型
 */
const getFormTypeFromParamType = (type: string) => {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();

  switch (lowerType) {
    case 'number':
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
      return 'number';

    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';

    case 'boolean':
    case 'bool':
      return 'checkbox';

    case 'select':
    case 'enum':
      return 'select';

    case 'textarea':
    case 'text_area':
      return 'textarea';

    default:
      return 'text';
  }
};

/**
 * 将字段导入为查询参数
 */
const loadFieldsAsQueryParams = async () => {
  try {
    let fields = await fetchFieldsFromDataSource();
    if (!Array.isArray(fields)) fields = [];
    console.log('[ImportFields] fetchFieldsFromDataSource 返回:', fields);

    if (fields.length === 0) {
      message.info('未从数据源获取到字段');
      return [];
    }

    console.log(`[ImportFields] 开始处理${fields.length}个字段`);

    // 将字段转换为参数格式
    const newParams = fields.map((field: any, index: number) => {
      // 确保field是对象格式
      const fieldObj = typeof field === 'string' ? { name: field } : field;

      // 获取原始类型和标签
      const originalType = fieldObj.type || 'String';
      const label = fieldObj.label || fieldObj.name;

      // 转换数据类型为参数类型
      const paramType = convertDataTypeToParamType(originalType);

      // 根据参数类型获取表单类型
      const formType = getFormTypeFromParamType(originalType);

      console.log(`[ImportFields] 处理字段: ${fieldObj.name}, 原始类型: ${originalType}, 转换为参数类型: ${paramType}, 表单类型: ${formType}`);

      // 确定正确的format值
      let format = originalType.toLowerCase();

      // 对于数字类型，明确设置为int或decimal
      if (paramType === 'number') {
        if (originalType.toLowerCase() === 'integer' || originalType.toLowerCase() === 'int' ||
            originalType.toLowerCase().includes('int') || originalType.toLowerCase() === 'number') {
          format = 'int';
        } else if (originalType.toLowerCase().includes('decimal') ||
                  originalType.toLowerCase().includes('float') ||
                  originalType.toLowerCase().includes('double')) {
          format = 'decimal';
        } else {
          // 默认为int，确保number类型始终有正确的format
          format = 'int';
          console.log(`[ImportFields] 未识别的数字类型 ${originalType}，默认设置为int格式`);
        }
      }

      // 创建参数对象
      const paramObj: QueryParam = {
        name: fieldObj.name,
        type: paramType,
        description: label,
        format: format,
        formType: formType,
        required: false,
        isNewParam: true,
        options: [],
        displayOrder: index,
        tableName: fieldObj.tableName || '',  // 保留表名属性
        isEncrypted: fieldObj.isEncrypted || false  // 保留加密标志
      };

      // 针对特殊类型设置额外属性
      if (paramType === 'number') {
        // 为数字类型添加默认验证范围
        if (originalType.toLowerCase() === 'integer' || originalType.toLowerCase() === 'int') {
          paramObj.minValue = -2147483648;  // INT的最小值
          paramObj.maxValue = 2147483647;   // INT的最大值

          // 设置数字格式配置
          paramObj.format = 'int';

          // 确保config对象存在
          if (!paramObj.config) {
            paramObj.config = {};
          }

          // 设置数字类型默认配置
          paramObj.config.fixedPoint = "0";
          paramObj.config.thousandSeparator = true;

          // 同步到顶级属性，以便在UI中显示
          paramObj.fixedPoint = paramObj.config.fixedPoint;
          paramObj.thousandSeparator = paramObj.config.thousandSeparator;

          console.log(`[ImportFields] 设置整数格式配置: format=${paramObj.format}, fixedPoint=${paramObj.config.fixedPoint}, thousandSeparator=${paramObj.config.thousandSeparator}`);
        } else if (originalType.toLowerCase().includes('decimal') ||
                  originalType.toLowerCase().includes('float') ||
                  originalType.toLowerCase().includes('double')) {
          // 设置数字格式配置
          paramObj.format = 'decimal';

          // 确保config对象存在
          if (!paramObj.config) {
            paramObj.config = {};
          }

          // 设置数字类型默认配置
          paramObj.config.fixedPoint = "2";
          paramObj.config.thousandSeparator = true;

          // 同步到顶级属性，以便在UI中显示
          paramObj.fixedPoint = paramObj.config.fixedPoint;
          paramObj.thousandSeparator = paramObj.config.thousandSeparator;

          console.log(`[ImportFields] 设置小数格式配置: format=${paramObj.format}, fixedPoint=${paramObj.config.fixedPoint}, thousandSeparator=${paramObj.config.thousandSeparator}`);
        }
      } else if (paramType === 'string') {
        // 为字符串类型添加默认最大长度
        paramObj.maxLength = 255;
      } else if (paramType === 'date') {
        // 为日期类型设置默认格式
        paramObj.dateFormat = 'YYYY-MM-DD';
      }

      return paramObj;
    });

    console.log('[ImportFields] 已生成参数列表:', newParams);

    // 打印最终生成的参数列表，用于调试
    console.log('[ImportFields] 最终生成的参数列表详情:');
    newParams.forEach((param, index) => {
      console.log(`[ImportFields] 参数${index+1}: name=${param.name}, type=${param.type}, format=${param.format}`);
      if (param.type === 'number') {
        console.log(`[ImportFields] 数字参数详情: format=${param.format}, config=${JSON.stringify(param.config)}`);
      }
    });

    // 初始化参数值
    const newParamValues: Record<string, any> = {};
    newParams.forEach((param: QueryParam) => {
      newParamValues[param.name] = param.defaultValue || '';
    });

    // 通知父组件参数更新
    console.log('[ImportFields] emit params-loaded:', newParams);
    emit('params-loaded', newParams);
    emit('param-values-updated', newParamValues);

    // 添加成功提示
    message.success(`成功从数据源导入${fields.length}个字段作为参数`);

    // 刷新组件
    if (props.queryConditionsPanelRef) {
      setTimeout(() => {
        try {
          // 更新 QueryConditionsPanel 的 availableFields
          if (props.queryConditionsPanelRef.updateAvailableFields) {
            console.log('[ImportFields] 更新 QueryConditionsPanel 的 availableFields，原始字段数量:', fields.length);
            // 转换字段格式，确保 updateAvailableFields 接收到正确的字段数据
            const availableFieldsData = fields.map((field: any) => {
              // 确保field是对象格式
              const fieldObj = typeof field === 'string' ? { name: field } : field;
              return {
                name: fieldObj.name,
                type: fieldObj.type || 'string',
                label: fieldObj.label || fieldObj.name,
                tableName: fieldObj.tableName || '',
                isEncrypted: fieldObj.isEncrypted || false
              };
            });
            props.queryConditionsPanelRef.updateAvailableFields(availableFieldsData);
          }

          if (props.queryConditionsPanelRef.refreshParamsList) {
            props.queryConditionsPanelRef.refreshParamsList();
          }
        } catch (e) {
          console.error('[ImportFields] 刷新参数列表失败:', e);
        }
      }, 100);
    }

    return newParams;
  } catch (error) {
    console.error('[ImportFields] 导入字段失败:', error);
    message.error('导入字段失败');
    return [];
  }
};

/**
 * 从查询ID加载参数
 */
const loadParamsFromQuery = async (): Promise<QueryParam[]> => {
  if (!props.integration.queryId) {
    message.warning('未设置查询ID，无法加载参数');
    return [];
  }

  try {
    console.log(`[DataSourceParamManager] 从查询ID加载参数: ${props.integration.queryId}`);

    // 调用API获取参数
    const response = await instance.get(`${getApiBaseUrl()}/api/queries/${props.integration.queryId}/parameters`);
    console.log('[DataSourceParamManager] API响应原始数据:', response);

    // 确保我们获取到正确的响应数据结构
    let responseData;
    if (response.data) {
      responseData = response.data;
    } else {
      responseData = response;
    }

    console.log('[DataSourceParamManager] 参数API响应:', responseData);

    // 处理数据，提取parameters数组
    let parameters: QueryParameterObject[] = [];

    // 尝试从不同的响应格式中提取参数
    if (responseData.success && responseData.data) {
      // 标准格式：{success: true, data: {parameters: []}}
      if (responseData.data.parameters && Array.isArray(responseData.data.parameters)) {
        parameters = responseData.data.parameters;
      }
      // 格式：{success: true, data: []}
      else if (Array.isArray(responseData.data)) {
        parameters = responseData.data as unknown as QueryParameterObject[];
      }
    }
    // 格式：直接返回参数数组
    else if (Array.isArray(responseData)) {
      parameters = responseData as unknown as QueryParameterObject[];
    }
    // 格式：{parameters: []}
    else if (responseData.parameters && Array.isArray(responseData.parameters)) {
      parameters = responseData.parameters;
    }

    console.log('[DataSourceParamManager] 提取到的原始参数:', parameters);

    if (parameters.length === 0) {
      console.log('[DataSourceParamManager] API返回的参数为空');
      return [];
    }

    // 转换为QueryParam格式
    const queryParams: QueryParam[] = parameters.map((param, index) => {
      // 确保param是对象
      const paramObj = typeof param === 'string' ? { name: param, type: 'string' } : param;

      return {
        name: paramObj.name,
        type: paramObj.type || 'string',
        description: paramObj.label || paramObj.name,
        format: getFormatFromType(paramObj.type),
        formType: getFormTypeFromType(paramObj.type),
        required: paramObj.required || false,
        displayOrder: index,
        defaultValue: paramObj.defaultValue,
        options: paramObj.options || [],
        isNewParam: true,  // 添加isNewParam标记
        tableName: paramObj.tableName || '',  // 保留表名属性
        isEncrypted: paramObj.isEncrypted || false  // 保留加密标志
      };
    });

    console.log('[DataSourceParamManager] 转换后的查询参数:', queryParams);

    if (queryParams.length > 0) {
      // 发出参数加载事件
      emit('params-loaded', queryParams);

      // 如果存在字段信息，也发出字段加载事件
      if (responseData.data && responseData.data.fields && Array.isArray(responseData.data.fields)) {
        emit('fields-loaded', responseData.data.fields);
      }

      // 更新参数值
      const newParamValues: Record<string, any> = {};

      queryParams.forEach(param => {
        if (param.defaultValue !== undefined) {
          newParamValues[param.name] = param.defaultValue;
        } else {
          // 设置默认空值
          switch (param.type) {
            case 'number':
              newParamValues[param.name] = null;
              break;
            case 'boolean':
              newParamValues[param.name] = false;
              break;
            default:
              newParamValues[param.name] = '';
          }
        }
      });

      // 发出参数值更新事件
      emit('param-values-updated', newParamValues);

      // 更新QueryConditionsPanel的availableFields
      if (props.queryConditionsPanelRef && props.queryConditionsPanelRef.updateAvailableFields) {
        // 仅当有fields字段时，更新availableFields
        if (responseData.data && responseData.data.fields && Array.isArray(responseData.data.fields)) {
          console.log('[DataSourceParamManager] 更新QueryConditionsPanel的availableFields，字段数量:', responseData.data.fields.length);
          props.queryConditionsPanelRef.updateAvailableFields(responseData.data.fields);
        } else {
          // 如果没有fields，则使用parameters代替
          console.log('[DataSourceParamManager] 没有fields字段，使用parameters更新availableFields，数量:', parameters.length);
          const fieldsFromParams = parameters.map(param => ({
            name: param.name,
            type: param.type || 'string',
            label: param.label || param.name
          }));
          props.queryConditionsPanelRef.updateAvailableFields(fieldsFromParams);
        }
      }
    }

    return queryParams;
  } catch (error) {
    console.error('[DataSourceParamManager] 加载参数失败:', error);
    message.error(`加载参数失败: ${error instanceof Error ? error.message : '未知错误'}`);

    return [];
  }
};

/**
 * 根据类型获取格式
 */
const getFormatFromType = (type: string | undefined): string => {
  if (!type) return 'string';

  const lowerType = type.toLowerCase();

  if (lowerType.includes('int') || lowerType.includes('number')) {
    return 'int';
  } else if (lowerType.includes('float') || lowerType.includes('double') || lowerType.includes('decimal')) {
    return 'decimal';
  } else if (lowerType.includes('date') && lowerType.includes('time')) {
    return 'date-time';
  } else if (lowerType.includes('date')) {
    return 'date';
  } else if (lowerType.includes('bool')) {
    return 'boolean';
  }

  return 'string';
};

/**
 * 根据类型获取表单类型
 */
const getFormTypeFromType = (type: string | undefined): string => {
  if (!type) return 'text';

  const lowerType = type.toLowerCase();

  if (lowerType.includes('int') || lowerType.includes('number') ||
      lowerType.includes('float') || lowerType.includes('double') ||
      lowerType.includes('decimal')) {
    return 'number';
  } else if (lowerType.includes('date') && lowerType.includes('time')) {
    return 'date';
  } else if (lowerType.includes('date')) {
    return 'date';
  } else if (lowerType.includes('bool')) {
    return 'checkbox';
  } else if (lowerType.includes('enum')) {
    return 'select';
  }

  return 'text';
};

// 导出组件方法
defineExpose({
  handleDataSourceSelected,
  handleLoadFieldsFromDataSource,
  fetchFieldsFromDataSource,
  loadFieldsAsQueryParams,
  loadParamsFromQuery
});
</script>
