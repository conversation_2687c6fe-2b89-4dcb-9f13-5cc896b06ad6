<template>
  <div>
    <!-- 版本管理组件 - 不需要UI，通过事件和属性与父组件交互 -->
  </div>
</template>

<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
// defineProps和defineEmits是编译器宏，不需要导入
import { ref, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import instance from '@/utils/axios';
import { getQueryApiUrl } from '@/services/apiUtils';
import type { VersionInfo } from '@/types/unified-integration';
import { getApiBasePath } from '@/utils/config';
import { getApiBaseUrl } from "@/services/query";

// 定义组件属性
const props = defineProps<{
  queryId: string;
  versionId?: string | VersionInfo;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'version-changed', version: VersionInfo | string): void;
  (e: 'versions-loaded', versions: Array<{id: string, versionNumber: number, isLatest: boolean}>): void;
  (e: 'loading-start'): void;
  (e: 'loading-end'): void;
}>();

// 本地状态
// 使用已导入的 message 服务
const versions = ref<Array<{id: string, versionNumber: number, isLatest: boolean}>>([]);
const isLoadingVersions = ref(false);

// 监听queryId变化
watch(() => props.queryId, async (newQueryId: string) => {
  if (newQueryId) {
    await loadQueryVersions(newQueryId);
  } else {
    versions.value = [];
    emit('versions-loaded', []);
  }
}, { immediate: true });

/**
 * 处理版本变更
 * @param versionInfo 版本信息对象或ID
 */
const handleVersionChange = (versionInfo: any) => {
  console.log('处理版本变更，接收到的版本信息:', versionInfo);

  try {
    // 如果传入的是字符串，说明是版本ID，需要查找对应的版本信息
    if (typeof versionInfo === 'string') {
      const version = versions.value.find(v => v.id === versionInfo);
      if (version) {
        const fullVersionInfo = {
          id: version.id,
          currentVersion: {
            id: version.id,
            versionNumber: version.versionNumber,
            isLatest: version.isLatest,
            status: 'ACTIVE'
          }
        };
        emit('version-changed', fullVersionInfo);
        console.log('根据版本ID找到的完整版本信息:', fullVersionInfo);
      } else {
        console.warn('未找到对应的版本信息:', versionInfo);
        // 使用默认版本信息
        const defaultVersion = {
          id: props.queryId || versionInfo,
          currentVersion: {
            id: props.queryId || versionInfo,
            versionNumber: 1,
            isLatest: true,
            status: 'ACTIVE'
          }
        };
        emit('version-changed', defaultVersion);
        console.log('使用默认版本信息:', defaultVersion);
      }
    } else if (typeof versionInfo === 'object' && versionInfo !== null) {
      // 确保对象有正确的结构
      const safeVersionInfo = {
        id: versionInfo.id || props.queryId || 'default',
        currentVersion: {
          id: versionInfo.currentVersion?.id || versionInfo.id || props.queryId || 'default',
          versionNumber: versionInfo.currentVersion?.versionNumber || 1,
          isLatest: versionInfo.currentVersion?.isLatest || true,
          status: versionInfo.currentVersion?.status || 'ACTIVE'
        }
      };
      emit('version-changed', safeVersionInfo);
      console.log('使用安全处理后的版本信息:', safeVersionInfo);
    } else {
      // 处理空值或其他类型
      console.warn('接收到无效的版本信息类型:', typeof versionInfo);
      const defaultVersion = {
        id: props.queryId || 'default',
        currentVersion: {
          id: props.queryId || 'default',
          versionNumber: 1,
          isLatest: true,
          status: 'ACTIVE'
        }
      };
      emit('version-changed', defaultVersion);
      console.log('使用默认版本信息:', defaultVersion);
    }
  } catch (error) {
    console.error('处理版本变更时出错:', error);
    // 出错时使用安全的默认值
    const fallbackVersion = {
      id: props.queryId || 'default',
      currentVersion: {
        id: props.queryId || 'default',
        versionNumber: 1,
        isLatest: true,
        status: 'ACTIVE'
      }
    };
    emit('version-changed', fallbackVersion);
    console.log('出错后使用兜底版本信息:', fallbackVersion);
  }
};

/**
 * 加载查询版本列表
 * @param queryId 查询ID
 */
const loadQueryVersions = async (queryId: string) => {
  if (!queryId) return;

  isLoadingVersions.value = true;
  emit('loading-start');
  console.log(`开始加载查询${queryId}的版本列表...`);

  try {
    // 使用正确的API路径格式
    const apiUrl = `${getApiBaseUrl()}/api/queries/${queryId}/versions?_t=${Date.now()}`;
    console.log(`请求版本列表: ${apiUrl}`);

    const response = await instance.get(apiUrl);
    console.log('接收到版本列表响应:', response);
    const result = response.data;

    // 判断响应是否包含预期的数据结构
    if (result && result.success && result.data) {
      // 支持嵌套data字段的情况
      const versionsData = result.data.items || result.data || [];

      // 遍历版本数据
      versions.value = Array.isArray(versionsData)
        ? versionsData.map((v: any) => ({
            id: v.id,
            versionNumber: v.versionNumber || 1,
            isLatest: v.isLatest || false
          }))
        : [];

      if (versions.value.length === 0) {
        console.log('未获取到版本数据，版本选择将保持为空');
        // 不再使用查询ID作为默认版本ID
        versions.value = [];
      } else if (versions.value.length === 1) {
        // 只有一个版本时自动选择
        const onlyVersion = versions.value[0];
        const versionInfo = {
          id: onlyVersion.id,
          currentVersion: {
            id: onlyVersion.id,
            versionNumber: onlyVersion.versionNumber,
            isLatest: onlyVersion.isLatest,
            status: 'ACTIVE'
          }
        };
        emit('version-changed', versionInfo);
        console.log(`只有一个版本，自动选择:`, versionInfo);
      } else {
        // 有多个版本时，不自动选择，让用户手动选择
        console.log(`检测到多个版本(${versions.value.length}个)，不自动选择，等待用户手动选择`);
        // 不发出version-changed事件，保持版本选择为空
      }

      // 向父组件发送版本列表
      emit('versions-loaded', versions.value);
    } else if (result && result.items) {
      // 直接支持包含items字段的分页响应结构
      const versionsData = result.items || [];

      versions.value = Array.isArray(versionsData)
        ? versionsData.map((v: any) => ({
            id: v.id,
            versionNumber: v.versionNumber || 1,
            isLatest: v.isLatest || false
          }))
        : [];

      if (versions.value.length === 0) {
        console.log('未获取到版本数据，版本选择将保持为空');
        // 不再使用查询ID作为默认版本ID
        versions.value = [];
      } else if (versions.value.length === 1) {
        // 只有一个版本时自动选择
        const onlyVersion = versions.value[0];
        const versionInfo = {
          id: onlyVersion.id,
          currentVersion: {
            id: onlyVersion.id,
            versionNumber: onlyVersion.versionNumber,
            isLatest: onlyVersion.isLatest,
            status: 'ACTIVE'
          }
        };
        emit('version-changed', versionInfo);
        console.log(`只有一个版本，自动选择:`, versionInfo);
      } else {
        // 有多个版本时，不自动选择，让用户手动选择
        console.log(`检测到多个版本(${versions.value.length}个)，不自动选择，等待用户手动选择`);
        // 不发出version-changed事件，保持版本选择为空
      }

      // 向父组件发送版本列表
      emit('versions-loaded', versions.value);
    } else {
      console.warn('API请求成功但返回格式不符合预期:', result);
      message.warning('获取查询版本失败，请手动选择版本');

      // 不再创建默认版本
      versions.value = [];
      emit('versions-loaded', versions.value);
    }
  } catch (error) {
    console.error('加载查询版本失败:', error);
    message.error('加载查询版本失败，请手动选择版本');

    // 不再创建默认版本
    versions.value = [];
    emit('versions-loaded', versions.value);
  } finally {
    isLoadingVersions.value = false;
    emit('loading-end');
    console.log('版本加载完成，当前版本列表:', versions.value);
  }
};

// 导出组件方法
defineExpose({
  handleVersionChange,
  loadQueryVersions
});
</script>
