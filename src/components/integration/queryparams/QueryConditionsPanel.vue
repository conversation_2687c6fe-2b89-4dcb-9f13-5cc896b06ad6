<template>
  <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
    <div class="px-4 py-5 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-900 flex justify-between items-center">
      <div>数据条件配置 {{ queryId ? `(ID: ${queryId})` : '' }}</div>
      <div v-if="queryId" class="flex space-x-2">
        <button
          type="button"
          class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-indigo-600 bg-white hover:bg-gray-50 focus:outline-none"
          @click="loadParamsFromQuery"
          :disabled="!queryId"
        >
          <i class="fas fa-sync-alt mr-1"></i>
          从数据源加载条件
        </button>
      </div>
    </div>
    <div class="p-6">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 data-params-table">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">排序</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">表名</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">字段</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">字段类型</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">中文名称</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">数据格式</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">表单类型</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">默认值</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">必填</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">操作</th>
            </tr>
          </thead>
          <draggable
            v-model="queryParams"
            tag="tbody"
            handle=".drag-handle"
            :animation="150"
            item-key="name"
            class="bg-white divide-y divide-gray-200"
            @change="onParamDragChange"
            :key="`params-list-${queryParams.length}`"
          >
            <template #item="{element: param, index}">
              <tr class="hover:bg-gray-50">
                <!-- 拖拽手柄 -->
                <td class="px-2 py-2 whitespace-nowrap">
                  <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 flex justify-center items-center w-full h-full">
                    <i class="fas fa-grip-vertical"></i>
                  </div>
                </td>

                <!-- 表名 -->
                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                  {{ param.tableName || '' }}
                </td>

                <!-- 字段 -->
                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                  {{ param.name || '' }}
                </td>

                <!-- 字段类型 -->
                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                  {{ param.type || '' }}
                </td>

                <!-- 中文名称 -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <input
                    v-model="param.description"
                    class="uniform-height-input ds-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="参数描述"
                  />
                </td>

                <!-- 数据格式 -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <div class="dropdown-menu-container">
                    <select
                      v-model="param.format"
                      class="uniform-height-input ds-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="string">字符串</option>
                      <option value="int">整数</option>
                      <option value="decimal">小数</option>
                      <option value="enum">枚举</option>
                      <option value="date">日期</option>
                      <option value="date-time">日期时间</option>
                      <option value="card">身份证</option>
                      <option value="mobile">手机号</option>
                      <option value="uri">链接</option>
                      <option value="email">邮箱</option>
                      <option value="json">JSON</option>
                      <option value="boolean">布尔值</option>
                    </select>
                  </div>
                </td>

                <!-- 表单类型 -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <div class="dropdown-menu-container">
                    <select
                      v-model="param.formType"
                      class="uniform-height-input ds-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="text">文本框</option>
                      <option value="textarea">多行文本</option>
                      <option value="number">数字输入框</option>
                      <option value="date">日期选择器</option>
                      <option value="date-range">日期时间区间</option>
                      <option value="select">下拉选择</option>
                    </select>
                  </div>
                </td>

                <!-- 参数值(原默认值列) -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <div v-if="param.type === 'date'">
                    <input
                      type="date"
                      v-model="paramValues[param.name]"
                      class="uniform-height-input ds-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      :class="{ 'border-red-300': validationErrors[param.name] }"
                    />
                  </div>
                  <!-- 枚举值特殊处理 -->
                  <div v-else-if="param.format === 'enum'" class="space-y-2">
                    <div class="flex space-x-2">
                      <select
                        v-model="paramValues[param.name]"
                        class="uniform-height-input ds-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm flex-1"
                        :class="{ 'border-red-300': validationErrors[param.name] }"
                      >
                        <option value="">请选择</option>
                        <option
                          v-for="option in param.options || []"
                          :key="option.value"
                          :value="option.value"
                        >
                          {{ option.label || option.value }}
                        </option>
                      </select>
                      <button
                        @click="openEnumSelector(param, index)"
                        type="button"
                        class="px-2 py-1 border border-transparent text-xs rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none"
                        title="从枚举加载"
                      >
                        <i class="fas fa-database"></i>
                      </button>
                    </div>
                  </div>
                  <!-- 表单类型为下拉选择时，默认值也显示为下拉框 -->
                  <div v-else-if="param.formType === 'select'" class="space-y-2">
                    <select
                      v-model="paramValues[param.name]"
                      class="uniform-height-input ds-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm w-full"
                      :class="{ 'border-red-300': validationErrors[param.name] }"
                    >
                      <option value="">请选择</option>
                      <option
                        v-for="option in param.options || []"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label || option.value }}
                      </option>
                    </select>
                  </div>
                  <input
                    v-else-if="param.type !== 'boolean'"
                    v-model="paramValues[param.name]"
                    :type="param.type === 'number' ? 'number' : 'text'"
                    class="uniform-height-input ds-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    :class="{ 'border-red-300': validationErrors[param.name] }"
                    :placeholder="param.description || `请输入值`"
                  />
                  <div v-else class="flex items-center uniform-height-input-checkbox">
                    <input
                      type="checkbox"
                      v-model="paramValues[param.name]"
                      class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      :id="'param-value-' + param.name"
                    />
                    <label :for="'param-value-' + param.name" class="ml-2 text-sm text-gray-700">
                      {{ param.description || param.name }}
                    </label>
                  </div>
                  <!-- 验证错误提示 -->
                  <p v-if="validationErrors[param.name]" class="mt-1 text-sm text-red-600">
                    {{ validationErrors[param.name] }}
                  </p>
                </td>

                <!-- 必填 -->
                <td class="px-2 py-2 whitespace-nowrap text-center">
                  <input
                    type="checkbox"
                    v-model="param.required"
                    class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </td>

                <!-- 操作 -->
                <td class="px-2 py-2 whitespace-nowrap text-center text-sm font-medium">
                  <button
                    @click="openAdvancedConfig(param, index)"
                    class="text-indigo-600 hover:text-indigo-900 mr-1"
                    title="高级配置"
                  >
                    配置
                  </button>
                  <button
                    @click="removeQueryParam(index)"
                    class="text-red-600 hover:text-red-900"
                  >
                    删除
                  </button>
                </td>
              </tr>
            </template>
          </draggable>
          <tfoot>
            <tr>
              <td colspan="10" class="px-4 py-3">
                <button
                  v-if="shouldShowAddButton"
                  @click="showAddParamModal = true"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
                >
                  <i class="fas fa-plus mr-1"></i> 添加参数
                </button>
                <span v-else-if="queryParams.length === 0" class="text-sm text-gray-500">
                  暂无参数，请从数据源加载
                </span>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>

    <!-- 使用新的参数配置组件 -->
    <ParamAdvancedConfig
      v-model="currentParam"
      :is-visible="showAdvancedConfigModal"
      :project-code="enumServiceConfig.projectCode"
      @close="closeAdvancedConfigModal"
      @save="saveAdvancedConfig"
    />

    <!-- 添加参数对话框 -->
    <div v-if="showAddParamModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-96 mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">添加参数</h3>
        </div>

        <div class="p-6">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">选择字段</label>
            <select
              v-model="selectedField"
              class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="">请选择字段...</option>
              <option
                v-for="field in nonAddedFields"
                :key="field.name"
                :value="field.name"
              >
                {{ field.label || field.name }}
              </option>
            </select>
          </div>

          <!-- 表名显示 -->
          <div class="mb-4" v-if="selectedField && selectedFieldInfo">
            <label class="block text-sm font-medium text-gray-700 mb-1">所属表</label>
            <div class="block w-full border border-gray-300 rounded-md py-2 px-3 bg-gray-50 text-gray-500 sm:text-sm">
              {{ selectedFieldInfo.tableName || '未知表' }}
            </div>
          </div>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showAddParamModal = false"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            取消
          </button>
          <button
            @click="confirmAddParam"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
            :disabled="!selectedField"
          >
            确认
          </button>
        </div>
      </div>
    </div>

    <!-- 枚举选择器 -->
    <EnumSelector
      v-if="showEnumSelectorModal"
      :project-code="enumServiceConfig.projectCode"
      :is-debug="false"
      @select="handleEnumSelect"
      @close="showEnumSelectorModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import draggable from 'vuedraggable';
import ParamAdvancedConfig from './ParamAdvancedConfig.vue';
import EnumSelector from './enum/EnumSelector.vue';
import { message } from '@/services/message';
import type { QueryParam } from '@/types/unified-integration';
import { enumServiceConfig } from '@/utils/config';
import { getApiBaseUrl } from "@/services/query";
import instance from '@/utils/axios';

// 定义组件接口
interface Props {
  modelValue: QueryParam[]; // 查询参数数组
  paramValues: Record<string, any>; // 参数值对象
  validationErrors: Record<string, string>; // 验证错误信息
  queryId?: string; // 查询ID
  dataSourceId?: string; // 数据源ID
  integrationType: string; // 集成类型
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'update:paramValues', 'loadParamsFromQuery', 'validate']);

// 存储查询参数的数组
const queryParams = ref<QueryParam[]>([]);

// 监听相关代码
watch(
  () => props.modelValue,
  (val: QueryParam[]) => {
    if (val && JSON.stringify(val) !== JSON.stringify(queryParams.value)) {
      console.log('[QueryConditionsPanel] modelValue changed:', val);

      // 打印详细的参数信息，特别是数字类型的参数
      if (val && val.length > 0) {
        console.log('[QueryConditionsPanel] 接收到的参数详情:');
        val.forEach((param, index) => {
          console.log(`[QueryConditionsPanel] 参数${index+1}: name=${param.name}, type=${param.type}, format=${param.format}`);
          if (param.type === 'number') {
            console.log(`[QueryConditionsPanel] 数字参数详情: format=${param.format}, config=${JSON.stringify(param.config)}`);
          }
        });
      }

      // 深拷贝参数
      queryParams.value = JSON.parse(JSON.stringify(val));

      // 确保数字类型参数的format值正确设置
      queryParams.value.forEach(param => {
        if (param.type === 'number') {
          // 确保format值为'int'或'decimal'
          if (param.format !== 'int' && param.format !== 'decimal') {
            if (param.format && (param.format.includes('int') || param.format === 'integer' || param.format === 'number')) {
              param.format = 'int';
              console.log(`[QueryConditionsPanel] 修正整数参数format: ${param.name}, format=${param.format}`);
            } else if (param.format && (param.format.includes('decimal') || param.format.includes('float') || param.format.includes('double'))) {
              param.format = 'decimal';
              console.log(`[QueryConditionsPanel] 修正小数参数format: ${param.name}, format=${param.format}`);
            } else {
              // 如果没有明确的类型信息，默认设置为int
              param.format = 'int';
              console.log(`[QueryConditionsPanel] 未识别的数字类型参数format: ${param.name}, 默认设置为int`);
            }

            // 确保config对象存在
            if (!param.config) {
              param.config = {};
            }

            // 设置数字类型默认配置
            param.config.fixedPoint = param.format === 'decimal' ? "2" : "0";
            param.config.thousandSeparator = true;

            // 同步到顶级属性，以便在UI中显示
            param.fixedPoint = param.config.fixedPoint;
            param.thousandSeparator = param.config.thousandSeparator;

            console.log(`[QueryConditionsPanel] 初始化时设置数字格式配置: name=${param.name}, format=${param.format}, fixedPoint=${param.config.fixedPoint}, thousandSeparator=${param.config.thousandSeparator}`);
          }
        }
      });
    }
  },
  { immediate: true }
);

// 监听外部传入的paramValues变化
watch(
  () => props.paramValues,
  (newVal: Record<string, any>) => {
    console.log('[QueryConditionsPanel] paramValues changed:', newVal);
  }
);

// 监听queryParams变化以发出更新事件
watch(
  () => queryParams.value,
  (newVal: QueryParam[]) => {
    console.log('[QueryConditionsPanel] queryParams changed:', newVal);
    emit('update:modelValue', newVal);
  }
);

// 组件挂载时记录初始数据
onMounted(() => {
  // 初始化操作
});

// 配置相关状态
const showAdvancedConfigModal = ref(false);
const currentParam = ref<QueryParam | null>(null);
const currentParamIndex = ref<number>(-1);

// 枚举选择器相关状态
const showEnumSelectorModal = ref(false);

// 添加可用字段状态
const availableFields = ref<Array<{name: string, type: string, label: string, tableName?: string, isEncrypted?: boolean}>>([]);
// 选择的字段
const selectedField = ref('');
// 添加参数对话框显示状态
const showAddParamModal = ref(false);

// 计算未添加的字段列表
const nonAddedFields = computed(() => {
  // 获取已添加字段名列表
  const addedFieldNames = new Set(queryParams.value.map(param => param.name));

  // 记录计算过程
  console.log('[QueryConditionsPanel] 计算未添加字段:', {
    '已添加字段数量': addedFieldNames.size,
    '已添加字段': Array.from(addedFieldNames).join(', '),
    '可用字段数量': availableFields.value.length,
    '可用字段': availableFields.value.map(f => f.name).join(', ')
  });

  // 过滤出未添加的字段
  const nonAdded = availableFields.value.filter(field => !addedFieldNames.has(field.name));

  console.log('[QueryConditionsPanel] 计算结果 - 未添加字段:', {
    '数量': nonAdded.length,
    '字段': nonAdded.map(f => f.name).join(', ')
  });

  return nonAdded;
});

// 计算当前选中字段的信息
const selectedFieldInfo = computed(() => {
  if (!selectedField.value) return null;
  return availableFields.value.find(field => field.name === selectedField.value) || null;
});

// 检查是否应该显示添加参数按钮
const shouldShowAddButton = computed(() => {
  // 只要有未添加的字段，就显示"添加"按钮
  return nonAddedFields.value.length > 0;
});

// 打开配置
const openAdvancedConfig = (param: QueryParam, index: number) => {
  currentParam.value = JSON.parse(JSON.stringify(param)); // 深拷贝以便取消操作
  currentParamIndex.value = index;
  showAdvancedConfigModal.value = true;
};

// 关闭配置
const closeAdvancedConfigModal = () => {
  showAdvancedConfigModal.value = false;
  currentParam.value = null;
  currentParamIndex.value = -1;
};

// 保存配置
const saveAdvancedConfig = (param: QueryParam) => {
  console.log('[QueryConditionsPanel] 接收到配置保存事件:', JSON.stringify(param));

  if (param && currentParamIndex.value >= 0) {
    // 确保保留原始的displayOrder值
    const originalDisplayOrder = queryParams.value[currentParamIndex.value].displayOrder;

    // 更新参数对象
    queryParams.value[currentParamIndex.value] = {
      ...param,
      displayOrder: originalDisplayOrder // 保留原始的顺序值
    };

    console.log('[QueryConditionsPanel] 更新后的查询参数:', JSON.stringify(queryParams.value[currentParamIndex.value]));

    // 获取当前UI表单中的值，并设置为参数的默认值
    if (props.paramValues && param.name in props.paramValues && props.paramValues[param.name] !== undefined && props.paramValues[param.name] !== null) {
      const currentValue = props.paramValues[param.name];
      console.log(`[QueryConditionsPanel] 设置参数 "${param.name}" 的默认值为当前值:`, currentValue);
      // 更新查询参数的默认值
      queryParams.value[currentParamIndex.value].defaultValue = currentValue;
    }

    // 确保更新传递到父组件
    emit('update:modelValue', queryParams.value);

    // 更新对应参数的初始值（如果设置了默认值）
    if (param.defaultValue !== undefined && props.paramValues) {
      props.paramValues[param.name] = param.defaultValue;
    }

    // 显示成功消息
    message.success(`参数 "${param.name}" 的配置已保存`);
  } else {
    console.error('[QueryConditionsPanel] 无法保存配置: 参数索引无效或参数对象为空');
  }

  closeAdvancedConfigModal();
};

// 添加一个强制刷新参数列表的方法
const refreshParamsList = () => {
  console.log('[QueryConditionsPanel] 开始刷新参数列表，当前参数:', queryParams.value);

  // 使用 nextTick 确保在 DOM 更新后执行
  nextTick(async () => {
    try {
      // 保存当前参数的深拷贝
      const currentParams = JSON.parse(JSON.stringify(queryParams.value));

      // 确保参数有正确的顺序
      currentParams.forEach((param: QueryParam, index: number) => {
        param.displayOrder = index;

        // 确保数字类型参数的format值正确设置
        if (param.type === 'number') {
          // 确保format值为'int'或'decimal'
          if (param.format !== 'int' && param.format !== 'decimal') {
            if (param.format && (param.format.includes('int') || param.format === 'integer' || param.format === 'number')) {
              param.format = 'int';
              console.log(`[QueryConditionsPanel] 刷新时修正整数参数format: ${param.name}, format=${param.format}`);
            } else if (param.format && (param.format.includes('decimal') || param.format.includes('float') || param.format.includes('double'))) {
              param.format = 'decimal';
              console.log(`[QueryConditionsPanel] 刷新时修正小数参数format: ${param.name}, format=${param.format}`);
            } else {
              // 如果没有明确的类型信息，默认设置为int
              param.format = 'int';
              console.log(`[QueryConditionsPanel] 刷新时未识别的数字类型参数format: ${param.name}, 默认设置为int`);
            }
          }

          // 确保config对象存在
          if (!param.config) {
            param.config = {};
          }

          // 设置数字类型默认配置
          if (param.format === 'int') {
            param.config.fixedPoint = "0";
          } else if (param.format === 'decimal') {
            param.config.fixedPoint = "2";
          }
          param.config.thousandSeparator = true;

          // 同步到顶级属性，以便在UI中显示
          param.fixedPoint = param.config.fixedPoint;
          param.thousandSeparator = param.config.thousandSeparator;

          console.log(`[QueryConditionsPanel] 刷新时设置数字格式配置: name=${param.name}, format=${param.format}, fixedPoint=${param.config.fixedPoint}, thousandSeparator=${param.config.thousandSeparator}`);
        }
      });

      // 直接更新参数，避免中间状态
      queryParams.value = currentParams;

      // 触发验证
      if (currentParams.length > 0) {
        emit('validate');
      }

      // 通知父组件参数已更新
      emit('update:modelValue', queryParams.value);

      console.log('[QueryConditionsPanel] 参数列表刷新完成:', queryParams.value);

      // 打印刷新后的参数列表
      console.log('[QueryConditionsPanel] 刷新后的参数列表:');
      queryParams.value.forEach((param, index) => {
        console.log(`[QueryConditionsPanel] 刷新后参数${index+1}: name=${param.name}, type=${param.type}, format=${param.format}`);
        if (param.type === 'number') {
          console.log(`[QueryConditionsPanel] 刷新后数字参数详情: format=${param.format}, config=${JSON.stringify(param.config)}`);
        }
      });
    } catch (error) {
      console.error('[QueryConditionsPanel] 刷新参数列表时出错:', error);
      message.error('刷新参数列表失败');
    }
  });
};

// 从数据源加载参数
const loadParamsFromQuery = async () => {
  console.log('[QueryConditionsPanel] 触发loadParamsFromQuery事件');
  emit('loadParamsFromQuery');
  // 所有实际的参数加载逻辑由父组件通过DataSourceParamManager处理
};

/**
 * 根据类型获取格式
 */
const getFormatFromType = (type: string): string => {
  const lowerType = type.toLowerCase();

  if (lowerType.includes('int') || lowerType.includes('number')) {
    return 'int';
  } else if (lowerType.includes('float') || lowerType.includes('double') || lowerType.includes('decimal')) {
    return 'decimal';
  } else if (lowerType.includes('date') && lowerType.includes('time')) {
    return 'date-time';
  } else if (lowerType.includes('date')) {
    return 'date';
  } else if (lowerType.includes('bool')) {
    return 'boolean';
  }

  return 'string';
};

/**
 * 根据类型获取表单类型
 */
const getFormTypeFromType = (type: string): string => {
  const lowerType = type.toLowerCase();

  if (lowerType.includes('int') || lowerType.includes('number') ||
      lowerType.includes('float') || lowerType.includes('double') ||
      lowerType.includes('decimal')) {
    return 'number';
  } else if (lowerType.includes('date') && lowerType.includes('time')) {
    return 'date';
  } else if (lowerType.includes('date')) {
    return 'date';
  } else if (lowerType.includes('bool')) {
    return 'checkbox';
  } else if (lowerType.includes('enum')) {
    return 'select';
  }

  return 'text';
};

// 确认添加参数
const confirmAddParam = () => {
  if (!selectedField.value) return;

  // 获取选中的字段完整信息
  const fieldInfo = selectedFieldInfo.value;
  if (!fieldInfo) {
    message.error('无法获取字段信息');
    return;
  }

  // 确定正确的format值
  let format = (fieldInfo.type || 'string').toLowerCase();
  const paramType = convertDataTypeToParamType(fieldInfo.type || 'string');

  // 对于数字类型，明确设置为int或decimal
  if (paramType === 'number') {
    if (format === 'integer' || format === 'int' || format.includes('int') || format === 'number') {
      format = 'int';
    } else if (format.includes('decimal') ||
              format.includes('float') ||
              format.includes('double')) {
      format = 'decimal';
    } else {
      // 如果没有明确的类型信息，默认设置为int
      format = 'int';
      console.log(`[QueryConditionsPanel] 添加参数时未识别的数字类型: ${fieldInfo.type}, 默认设置为int`);
    }
  }

  // 创建新参数
  const newParam: QueryParam = {
    name: fieldInfo.name,
    type: paramType,
    description: fieldInfo.label || fieldInfo.name,
    format: format,
    formType: getFormTypeFromParamType(fieldInfo.type || 'string'),
    required: false,
    isNewParam: true,
    options: [],
    displayOrder: queryParams.value.length,
    tableName: fieldInfo.tableName || '',
    isEncrypted: fieldInfo.isEncrypted || false
  };

  // 针对特殊类型设置额外属性
  if (newParam.type === 'number') {
    // 为数字类型添加默认验证范围
    if (newParam.format === 'integer' || newParam.format === 'int') {
      newParam.minValue = -2147483648;  // INT的最小值
      newParam.maxValue = 2147483647;   // INT的最大值

      // 设置数字格式配置
      newParam.format = 'int';

      // 确保config对象存在
      if (!newParam.config) {
        newParam.config = {};
      }

      // 设置数字类型默认配置
      newParam.config.fixedPoint = "0";
      newParam.config.thousandSeparator = true;

      // 同步到顶级属性，以便在UI中显示
      newParam.fixedPoint = newParam.config.fixedPoint;
      newParam.thousandSeparator = newParam.config.thousandSeparator;

      console.log(`[QueryConditionsPanel] 设置整数格式配置: format=${newParam.format}, fixedPoint=${newParam.config.fixedPoint}, thousandSeparator=${newParam.config.thousandSeparator}`);
    } else if (newParam.format.includes('decimal') ||
              newParam.format.includes('float') ||
              newParam.format.includes('double')) {
      // 设置数字格式配置
      newParam.format = 'decimal';

      // 确保config对象存在
      if (!newParam.config) {
        newParam.config = {};
      }

      // 设置数字类型默认配置
      newParam.config.fixedPoint = "2";
      newParam.config.thousandSeparator = true;

      // 同步到顶级属性，以便在UI中显示
      newParam.fixedPoint = newParam.config.fixedPoint;
      newParam.thousandSeparator = newParam.config.thousandSeparator;

      console.log(`[QueryConditionsPanel] 设置小数格式配置: format=${newParam.format}, fixedPoint=${newParam.config.fixedPoint}, thousandSeparator=${newParam.config.thousandSeparator}`);
    }
  } else if (newParam.type === 'string') {
    // 为字符串类型添加默认最大长度
    newParam.maxLength = 255;
  } else if (newParam.type === 'date') {
    // 为日期类型设置默认格式
    newParam.dateFormat = 'YYYY-MM-DD';
  }

  // 添加到参数列表
  queryParams.value.push(newParam);

  // 初始化参数值
  if (props.paramValues) {
    props.paramValues[newParam.name] = newParam.defaultValue || '';
  }

  // 关闭对话框并重置选择
  showAddParamModal.value = false;
  selectedField.value = '';
};

// 将数据类型转换为参数类型
const convertDataTypeToParamType = (type: string) => {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();

  switch (lowerType) {
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
    case 'number':
      return 'number';

    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';

    case 'boolean':
    case 'bool':
      return 'boolean';

    case 'string':
    case 'varchar':
    case 'char':
    case 'text':
    default:
      return 'string';
  }
};

// 获取表单类型
const getFormTypeFromParamType = (type: string) => {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();

  switch (lowerType) {
    case 'number':
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
      return 'number';

    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';

    case 'boolean':
    case 'bool':
      return 'checkbox';

    case 'select':
    case 'enum':
      return 'select';

    case 'textarea':
    case 'text_area':
      return 'textarea';

    case 'password':
      return 'password';

    default:
      return 'text';
  }
};

// 移除查询参数
const removeQueryParam = (index: number) => {
  // 保存待删除的参数名，用于调试
  const deletedParam = queryParams.value[index];
  console.log('[QueryConditionsPanel] 删除参数:', deletedParam.name);

  // 删除参数
  queryParams.value.splice(index, 1);

  // 更新显示顺序
  queryParams.value.forEach((param, idx) => {
    param.displayOrder = idx;
  });

  // 注意：当前实现中，删除参数后不会自动将其恢复到可用字段列表(availableFields)中
  // 这可能导致用户无法重新添加已删除的字段，除非重新从数据源加载
  // TODO: 考虑在删除参数时，将对应字段加回到availableFields列表

  // 强制发送更新事件，确保计算属性重新计算
  emit('update:modelValue', [...queryParams.value]);

  console.log('[QueryConditionsPanel] 删除后状态:', {
    '参数数量': queryParams.value.length,
    '可用字段数量': availableFields.value.length,
    '未添加字段数量': nonAddedFields.value.length,
    '显示添加按钮': shouldShowAddButton.value
  });
};

// 处理拖拽排序
const onParamDragChange = () => {
  // 更新显示顺序
  queryParams.value.forEach((param, index) => {
    param.displayOrder = index;
  });

  // 发出事件通知父组件排序已更改
  emit('update:modelValue', queryParams.value);

  // 提示用户排序已更新
  message.success('参数顺序已更新');

  console.log('[QueryConditionsPanel] 参数排序已更新，新的顺序:',
    queryParams.value.map(p => `${p.name}:${p.displayOrder}`).join(', '));
};

// 验证参数
const validateParams = (): boolean => {
  console.log('[QueryConditionsPanel] 开始参数验证, 集成类型:', props.integrationType);
  let isValid = true;
  const errors: Record<string, string> = {};
  const isChartMode = props.integrationType === 'CHART';

  // 在图表模式下，不验证是否有查询条件
  if (!isChartMode && (!queryParams.value || queryParams.value.length === 0)) {
    console.log('[QueryConditionsPanel] 验证失败: 至少需要一个查询条件');
    isValid = false;
    errors['_global'] = '至少需要一个查询条件';
  } else {
    console.log('[QueryConditionsPanel] 验证查询条件', isChartMode ? '(图表模式不要求条件)' : '(需要至少一个条件)');

    // 始终验证格式和类型，即使在图表模式下
    queryParams.value.forEach((param: QueryParam, index: number) => {
      const paramId = `param_${index}`;

      // 检查名称是否为空
      if (!param.name) {
        isValid = false;
        errors[paramId] = '参数名称不能为空';
      }

      // 检查类型是否合法
      if (!param.type) {
        isValid = false;
        errors[`${paramId}_type`] = '参数类型不能为空';
      }

      // 对于数值参数，检查范围
      if (param.type === 'number' && param.min !== undefined && param.max !== undefined && param.min > param.max) {
        isValid = false;
        errors[`${paramId}_range`] = '最小值不能大于最大值';
      }

      // 对于字符串参数，检查长度
      if (param.type === 'string' && param.minLength !== undefined && param.maxLength !== undefined && param.minLength > param.maxLength) {
        isValid = false;
        errors[`${paramId}_length`] = '最小长度不能大于最大长度';
      }

      // 枚举参数必须有选项
      if (param.type === 'enum' && (!param.options || param.options.length === 0)) {
        isValid = false;
        errors[`${paramId}_options`] = '枚举参数必须有选项';
      }
    });
  }

  console.log('[QueryConditionsPanel] 验证结果:', isValid ? '通过' : '失败', errors);
  emit('validate', errors);
  return isValid;
};

// 更新可用字段列表
const updateAvailableFields = (fields: Array<{name: string, type: string, label: string, tableName?: string, isEncrypted?: boolean}>) => {
  console.log('[QueryConditionsPanel] 更新可用字段开始:', {
    '传入字段数量': fields.length,
    '传入字段列表': fields.map(f => f.name).join(', ')
  });
  availableFields.value = fields;

  // 在更新后立即计算和记录状态
  nextTick(() => {
    console.log('[QueryConditionsPanel] 更新可用字段完成后状态:', {
      '可用字段数量': availableFields.value.length,
      '当前参数数量': queryParams.value.length,
      '未添加字段数量': nonAddedFields.value.length,
      '未添加字段': nonAddedFields.value.map(f => f.name).join(', '),
      '是否应显示添加按钮': shouldShowAddButton.value
    });
  });
};

// 打开枚举选择器
const openEnumSelector = (param: QueryParam, index: number) => {
  currentParam.value = param;
  currentParamIndex.value = index;
  showEnumSelectorModal.value = true;
};

// 处理枚举选择
const handleEnumSelect = (result: {
  options: Array<{label: string, value: string}>;
  enumId: string;
  enumName: string;
  enumCode: string;
}) => {
  if (currentParam.value && currentParamIndex.value >= 0) {
    console.log('[QueryConditionsPanel] 处理枚举选择，原始数据:', JSON.stringify(result));

    // 确保options有效
    if (!Array.isArray(result.options) || result.options.length === 0) {
      console.error('[QueryConditionsPanel] 枚举选项无效:', result.options);
      message.error('枚举选项无效，请选择包含有效选项的枚举或创建新枚举');
      return;
    }

    // 更新参数的选项列表
    queryParams.value[currentParamIndex.value].options = result.options;

    // 额外打印选项列表，确认数据正确
    console.log('[QueryConditionsPanel] 枚举选项已更新:',
      result.options.map(o => `${o.label} (${o.value})`).join(', '));

    // 保存枚举相关信息
    queryParams.value[currentParamIndex.value].enumId = result.enumId;
    queryParams.value[currentParamIndex.value].enumName = result.enumName;
    queryParams.value[currentParamIndex.value].enumCode = result.enumCode;

    // 确保格式为enum
    queryParams.value[currentParamIndex.value].format = 'enum';

    // 如果是当前没有选择值，则设置第一个选项为默认值
    if (props.paramValues && !props.paramValues[queryParams.value[currentParamIndex.value].name]) {
      console.log('[QueryConditionsPanel] 当前参数无值，设置默认值:', result.options[0].value);
      props.paramValues[queryParams.value[currentParamIndex.value].name] = result.options[0].value;
    }

    // 重新触发更新
    emit('update:modelValue', [...queryParams.value]);

    // 显示成功消息
    message.success(`已从枚举"${result.enumName}"加载${result.options.length}个选项`);

    // 关闭枚举选择器
    showEnumSelectorModal.value = false;

    // 重置状态
    currentParam.value = null;
    currentParamIndex.value = -1;
  } else {
    console.error('[QueryConditionsPanel] 当前没有选中参数，无法应用枚举');
  }
};

// 将方法暴露给父组件
defineExpose({
  addQueryParam: confirmAddParam,
  removeQueryParam,
  validateParams,
  refreshParamsList,
  updateAvailableFields
});
</script>

<style scoped>
.data-params-table th,
.data-params-table td {
  vertical-align: middle;
  white-space: normal;
  word-break: break-word;
}

.force-border {
  border: 1px solid #d1d5db !important;
}

/* 统一输入框和下拉框高度 */
.uniform-height-input {
  height: 38px;
  min-height: 38px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.uniform-height-input-checkbox {
  height: 38px;
  min-height: 38px;
  display: flex;
  align-items: center;
}

/* 确保所有表单元素使用相同的边框和内边距 */
input.uniform-height-input,
select.uniform-height-input,
.uniform-height-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  line-height: 1.25;
}

/* 确保下拉菜单在所有浏览器中具有一致的外观 */
select.uniform-height-input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  min-width: 100px;
  width: 100%;
}

/* 确保表格内容不会被截断 */
.data-params-table {
  table-layout: fixed;
  width: 100%;
}
</style>
