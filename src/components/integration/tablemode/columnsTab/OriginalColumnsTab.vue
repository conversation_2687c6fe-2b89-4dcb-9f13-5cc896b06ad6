<template>
  <div class="overflow-visible">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">排序</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12"></th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">字段</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">字段类型</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">中文名称</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据格式</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">列格式</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">列宽</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <draggable
        v-model="columns"
        tag="tbody"
        handle=".drag-handle"
        :animation="150"
        item-key="field"
        class="bg-white divide-y divide-gray-200"
        @change="onDragChange"
      >
        <template #item="{element: column, index}">
          <tr class="hover:bg-gray-50">
            <!-- 拖拽手柄 -->
            <td class="px-2 py-2 whitespace-nowrap">
              <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 flex justify-center items-center w-full h-full">
                <i class="fas fa-grip-vertical"></i>
              </div>
            </td>

            <!-- 是否加密 -->
            <td class="px-2 py-2 whitespace-nowrap text-center text-sm w-12">
              <span v-if="column.isEncrypted === true" class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <i class="fas fa-lock"></i>
              </span>
              <span v-else class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                <i class="fas fa-unlock"></i>
              </span>
            </td>

          <!-- 字段 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <div class="relative">
                <div
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md cursor-pointer border border-gray-300 field-selector"
                  :class="{
                    'bg-gray-100': !column.isNewColumn,
                    'cursor-not-allowed': !column.isNewColumn,
                    'cursor-pointer': column.isNewColumn,
                    'active': activeFieldDropdown === index
                  }"
                  @click.stop="toggleFieldDropdown(column, index, $event)"
                >
                  <div class="flex items-center p-2">
                    <span class="flex-1 truncate">{{ column.field || '请选择字段' }}</span>
                  </div>
                </div>

                <div
                  v-if="activeFieldDropdown === index && column.isNewColumn"
                  class="absolute left-0 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-50"
                  @click.stop
                >
                  <div class="p-2 border-b">
                    <input
                      v-model="fieldSearchText"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 field-search-input"
                      placeholder="搜索字段..."
                      @click.stop
                      @keydown.esc="closeFieldDropdown()"
                      ref="fieldSearchInput"
                    />
                  </div>
                  <div class="max-h-60 overflow-y-auto p-0">
                    <div
                      v-for="field in filteredAvailableFields"
                      :key="field"
                      class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                      @click.stop="selectField(column, field); $event.preventDefault();"
                      @mousedown.prevent
                    >
                      {{ field }}
                    </div>
                    <div
                      v-if="filteredAvailableFields.length === 0"
                      class="px-3 py-2 text-gray-500 text-sm"
                    >
                      无匹配字段
                    </div>
                  </div>
                </div>
              </div>
            </td>

            <!-- 字段类型 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <input
                v-model="column.type"
                class="form-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                disabled
                :class="{'bg-gray-100': true}"
                placeholder="字段类型"
              />
            </td>

            <!-- 中文名称 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <input
                v-model="column.label"
                class="form-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="显示名称"
              />
            </td>

            <!-- 数据格式 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <select
                v-model="column.format"
                class="form-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                @change="handleFormatChange(column)"
              >
                <option value="string">字符串</option>
                <option value="int">整数</option>
                <option value="decimal">小数</option>
                <option value="enum">枚举</option>
                <option value="date">日期</option>
                <option value="date-time">日期时间</option>
                <option value="card">卡号</option>
                <option value="mobile">手机号</option>
                <option value="uri">URI</option>
                <option value="email">邮箱</option>
                <option value="json">JSON</option>
                <option value="boolean">布尔值</option>
              </select>
            </td>

            <!-- 列格式 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <select
                v-model="column.align"
                class="form-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              >
                <option :value="columnAlign.LEFT">左对齐</option>
                <option :value="columnAlign.CENTER">居中</option>
                <option :value="columnAlign.RIGHT">右对齐</option>
              </select>
            </td>

            <!-- 列宽 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <input
                v-model="column.width"
                class="form-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="列宽"
              />
            </td>



            <!-- 操作 -->
            <td class="px-4 py-2 whitespace-nowrap text-left text-sm font-medium">
              <button
                @click="editColumn(column)"
                class="text-indigo-600 hover:text-indigo-900 mr-2"
              >
                <i class="fas fa-cog mr-1"></i>更多设置
              </button>
              <button
                @click="removeColumn(index)"
                class="text-red-600 hover:text-red-900"
              >
                <i class="fas fa-trash-alt mr-1"></i>删除
              </button>
            </td>
          </tr>
        </template>
      </draggable>
      <tfoot>
        <tr>
          <td colspan="9" class="px-4 py-3">
            <button
              v-if="showAddColumnButton"
              @click="addColumn"
              class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
            >
              <i class="fas fa-plus mr-1"></i> 添加列配置
            </button>
            <span v-else class="text-sm text-gray-500">{{ availableFields && availableFields.value && availableFields.value.length === 0 ? '请先导入字段，再添加列配置' : '已导入所有可用字段，无法添加更多列' }}</span>
          </td>
        </tr>
      </tfoot>
    </table>

    <!-- 列编辑弹窗 -->
    <div v-if="showColumnEditModal && editingColumn" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-20">
      <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
        <!-- 弹窗标题 -->
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">编辑列配置</h3>
          <button @click="closeColumnEditModal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 弹窗内容 -->
        <div class="px-6 py-4 flex-grow overflow-y-auto">
          <div class="space-y-4">
            <!-- 配置区域 -->
            <div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 可见性 -->
                <div class="flex items-center">
                  <input
                    id="column-visible"
                    type="checkbox"
                    v-model="editingColumn.visible"
                    class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                  />
                  <label for="column-visible" class="ml-2 block text-sm text-gray-900">显示该列</label>
                </div>

                <!-- 可排序 -->
                <div class="flex items-center">
                  <input
                    id="column-sortable"
                    type="checkbox"
                    v-model="editingColumn.sortable"
                    class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                  />
                  <label for="column-sortable" class="ml-2 block text-sm text-gray-900">允许排序</label>
                </div>

                <!-- 可过滤 -->
                <div class="flex items-center">
                  <input
                    id="column-filterable"
                    type="checkbox"
                    v-model="editingColumn.filterable"
                    class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                  />
                  <label for="column-filterable" class="ml-2 block text-sm text-gray-900">允许筛选</label>
                </div>

                <!-- 是否加密 -->
                <div class="flex items-center">
                  <div class="text-sm text-gray-900">
                    <span class="font-medium">是否加密: </span>
                    <span v-if="editingColumn.isEncrypted === true" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      <i class="fas fa-lock mr-1"></i> 是
                    </span>
                    <span v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      <i class="fas fa-unlock mr-1"></i> 否
                    </span>
                  </div>
                </div>
              </div>

              <!-- 数据格式配置区域 -->
              <div class="mt-4 border-t border-gray-200 pt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- 帮助文本 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">帮助文本</label>
                    <input
                      :value="editingColumn.helpText === undefined ? '' : editingColumn.helpText"
                      @input="event => editingColumn.helpText = (event.target as HTMLInputElement).value"
                      type="text"
                      class="form-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="输入对此列的说明文字"
                    />

                  </div>

                  <!-- 自动省略 -->
                  <div v-if="editingColumn.displayType === 'TEXT'">
                    <div class="flex items-center">
                      <input
                        id="auto-ellipsis"
                        type="checkbox"
                        v-model="editingColumn.truncate"
                        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                      />
                      <label for="auto-ellipsis" class="ml-2 block text-sm text-gray-900">自动省略</label>
                    </div>
                  </div>

                  <!-- 小数位数 -->
                  <div v-if="editingColumn.format === 'decimal'">
                    <label class="block text-sm font-medium text-gray-700 mb-1">小数位数</label>
                    <input
                      v-model.number="editingColumn.fixedPoint"
                      type="number"
                      min="0"
                      max="10"
                      class="form-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="保留小数位数"
                    />
                  </div>

                  <!-- 千位分隔符 -->
                  <div v-if="editingColumn.format === 'int' || editingColumn.format === 'decimal'">
                    <div class="flex items-center">
                      <input
                        id="thousand-separator"
                        type="checkbox"
                        v-model="editingColumn.thousandSeparator"
                        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                      />
                      <label for="thousand-separator" class="ml-2 block text-sm text-gray-900">启用千位分隔符</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 枚举配置 -->
              <div v-if="editingColumn.format === 'enum'" class="mt-4 border-t border-gray-200 pt-4">
                <h4 class="text-md font-medium text-gray-900 mb-3">枚举配置</h4>

                <!-- 枚举关联信息展示 -->
                <div v-if="editingColumn.enumId && editingColumn.enumName" class="bg-blue-50 rounded-md p-3 mb-3">
                  <div class="flex justify-between items-center">
                    <div>
                      <span class="text-sm font-medium text-blue-700">当前关联枚举: </span>
                      <span class="text-sm text-blue-800">{{ editingColumn.enumName }}</span>
                      <span class="text-xs text-gray-500 ml-2">({{ editingColumn.enumCode }})</span>
                    </div>
                  </div>
                </div>

                <!-- 打开配置按钮 -->
                <button
                  @click="openParamConfigModal"
                  class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <i class="fas fa-cog mr-2"></i> 配置枚举关联
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 弹窗底部按钮 -->
        <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end">
          <button
            @click="closeColumnEditModal"
            class="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-3 focus:outline-none"
          >
            取消
          </button>
          <button
            @click="saveColumnEdit"
            class="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
          >
            保存
          </button>
        </div>
      </div>
    </div>

    <!-- 枚举选择器对话框 -->
    <EnumSelector
      v-if="showEnumSelector"
      :projectCode="enumProjectCode"
      @select="handleEnumSelected"
      @close="closeEnumSelector"
    />

    <!-- 参数配置弹窗 - 独立于列编辑弹窗 -->
    <div v-if="showParamConfigModal" class="param-config-modal" style="z-index: 3100;">
      <ParamAdvancedConfig
        v-if="columnParam"
        v-model="columnParam"
        :isVisible="showParamConfigModal"
        :projectCode="enumProjectCode"
        @save="handleParamSave"
        @close="closeParamConfigModal"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue';
import { message } from '@/services/message';
import draggable from 'vuedraggable';
import { ColumnDisplayType, ColumnAlign } from '@/types/integration';
import type { TableColumn, DisplayType } from '@/types/integration';
import EnumSelector from '@/components/integration/queryparams/enum/EnumSelector.vue';
import ParamAdvancedConfig from '@/components/integration/queryparams/ParamAdvancedConfig.vue';
import { enumServiceConfig } from '@/utils/config';
import { getRecommendedDisplayType, getDefaultFormat, getDefaultAlign, getDefaultWidth } from '@/utils/typeMapping';
import { syncConfigToTopLevel, syncTopLevelToConfig } from '@/utils/columnConfigSync';
import { getApiBaseUrl } from '@/services/query';
import instance from '@/utils/axios';

// 扩展TableColumn接口以包含isEncrypted等额外字段
interface ExtendedTableColumn extends TableColumn {
  isEncrypted?: boolean | string;
  helpText?: string;
  fixedPoint?: string | number;
  thousandSeparator?: boolean;
  truncate?: boolean;
  isNewColumn?: boolean;
  config?: {
    help?: string;
    fixedPoint?: string | number;
    thousandSeparator?: boolean;
    truncate?: boolean;
    [key: string]: any;
  };
}

// Props
const props = defineProps<{
  columns: TableColumn[];
  queryId?: string;
  queryVersionRef?: any;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:columns', columns: ExtendedTableColumn[]): void;
  (e: 'edit-column', column: ExtendedTableColumn): void;
}>();

// 列对齐方式常量
const columnAlign = {
  LEFT: ColumnAlign.LEFT,
  CENTER: ColumnAlign.CENTER,
  RIGHT: ColumnAlign.RIGHT
};

// 内部状态
const activeFieldDropdown = ref<number>(-1);
const fieldSearchText = ref('');
const fieldSearchInput = ref<HTMLInputElement | null>(null);
const showColumnEditModal = ref(false);
const showEnumSelector = ref(false);
const showParamConfigModal = ref(false);
const enumProjectCode = ref(enumServiceConfig.projectCode);
// 初始化为空数组，确保不会是undefined
const availableFields = ref<string[]>([]);
const fieldTypeMap = ref<Record<string, string>>({});
const editingColumn = ref<ExtendedTableColumn>({
  id: `temp_${Date.now()}`, // 添加id字段
  field: '',
  label: '',
  type: 'string',
  displayType: 'text', // 使用字符串字面量类型而不是枚举
  width: '100',
  align: ColumnAlign.LEFT,
  visible: true,
  sortable: false,
  filterable: false,
  displayOrder: 0,
  enumCode: '',
  enumDisplay: 'text',
  defaultEnumValue: ''
});

// 列表相关
type SafeWritableRef<T> = { value: T }
const columns = computed({
  get: () => {
    // 修复加密状态显示问题：确保isEncrypted是布尔类型
    const fixedColumns = props.columns.map((col: TableColumn) => {
      const extendedCol = col as ExtendedTableColumn;
      // 将isEncrypted字符串'true'或'false'转换为实际的布尔值
      if (typeof extendedCol.isEncrypted === 'string') {
        return {
          ...extendedCol,
          isEncrypted: extendedCol.isEncrypted === 'true' ? true : false
        };
      }
      return extendedCol;
    });
    return fixedColumns;
  },
  set: (value: ExtendedTableColumn[]) => {
    // 创建深拷贝避免引用问题，确保响应式更新
    const deepCopy = JSON.parse(JSON.stringify(value));
    console.log('[columns.set] 触发update:columns事件, 长度:', deepCopy.length);
    emit('update:columns', deepCopy);

    // 强制刷新UI
    nextTick(() => {
      console.log('[columns.set] 在nextTick中检查columns长度:', deepCopy.length);
    });
  }
}) as unknown as SafeWritableRef<ExtendedTableColumn[]>;

// 过滤可用字段
const filteredAvailableFields = computed(() => {
  // 首先检查availableFields是否存在
  if (!availableFields || !availableFields.value) return [];

  // 获取已使用的字段
  const usedFields = new Set(columns.value.map((col: TableColumn) => col.field).filter(Boolean));

  // 过滤掉已使用的字段
  const unusedFields = availableFields.value.filter(field => !usedFields.has(field));

  // 如果有搜索文本，在未使用的字段中进一步过滤
  if (!fieldSearchText.value) {
    return unusedFields;
  }

  const searchTerm = fieldSearchText.value.toLowerCase();
  return unusedFields.filter(field =>
    field.toLowerCase().includes(searchTerm)
  );
});

// 判断是否显示添加按钮
const showAddColumnButton = computed(() => {
  // 如果没有availableFields或者没有导入过字段，不显示添加按钮
  if (!availableFields || !availableFields.value || availableFields.value.length === 0) return false;

  // 获取当前已使用的字段
  const usedFields = new Set(columns.value.map((col: TableColumn) => col.field).filter(Boolean));

  // 检查是否还有未使用的字段
  const hasUnusedFields = availableFields.value.some(field => !usedFields.has(field));

  return hasUnusedFields;
});

// 将表格列转换为参数对象，用于ParamAdvancedConfig组件
const columnParam = computed(() => {
  if (!editingColumn.value) return null;

  return {
    name: editingColumn.value.field,
    label: editingColumn.value.label,
    type: 'string',
    format: 'enum',
    formType: 'select',
    required: false,
    description: editingColumn.value.label || editingColumn.value.field,
    displayOrder: editingColumn.value.displayOrder,
    enumId: editingColumn.value.enumId,
    enumName: editingColumn.value.enumName,
    enumCode: editingColumn.value.enumCode,
    options: [], // 这里需要从枚举服务获取
    multiSelect: false
  };
});

// 生命周期钩子
onMounted(async () => {
  // 如果有查询ID，预加载字段选项
  if (props.queryId) {
    loadFieldOptions();
  }

  // 调试代码：检查加密状态
  setTimeout(() => {
    console.log('[调试加密列] 当前列的加密状态:', columns.value.map((col: ExtendedTableColumn) => ({
      field: col.field,
      isEncrypted: col.isEncrypted === true ? 'true' : 'false',
      isEncryptedRawValue: col.isEncrypted,
      typeOfIsEncrypted: typeof col.isEncrypted
    })));
  }, 500);

  // 点击事件监听，关闭下拉菜单
  const handleDocumentClick = (event: MouseEvent) => {
    // 检查点击事件是否发生在下拉菜单内部
    const target = event.target as HTMLElement;
    const isFieldSelector = target.closest('.field-selector') !== null;
    const isFieldDropdown = target.closest('.absolute') !== null && !isFieldSelector;

    // 如果点击在下拉菜单和选择器之外，关闭下拉菜单
    if (!isFieldSelector && !isFieldDropdown && activeFieldDropdown.value !== -1) {
      closeFieldDropdown();
    }
  };

  // ESC键监听，关闭下拉菜单
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && activeFieldDropdown.value !== -1) {
      closeFieldDropdown();
    }
  };

  document.addEventListener('click', handleDocumentClick);
  document.addEventListener('keydown', handleKeyDown);

  // 组件卸载时清理事件监听器
  return () => {
    document.removeEventListener('click', handleDocumentClick);
    document.removeEventListener('keydown', handleKeyDown);
  };
});

// 加载可用字段选项
const loadFieldOptions = async () => {
  // 确保availableFields被初始化
  if (!availableFields.value) {
    availableFields.value = [];
  }

  // 如果没有queryId或者已经有字段，则返回
  if (!props.queryId || availableFields.value.length > 0) {
    return;
  }

  try {
    // 直接使用parameters接口获取字段列表
    const apiUrl = `/api/queries/${props.queryId}/parameters`;
    console.log(`[OriginalColumnsTab] 使用parameters接口获取字段: ${apiUrl}`);

    // 调用接口获取字段列表
    const response = await instance.get(apiUrl);
    const data = response.data;

    // 从返回的数据中提取fields字段
    if (data && data.fields && Array.isArray(data.fields)) {
      // 存储字段信息，包括name和type
      availableFields.value = data.fields.map((field: any) => field.name).filter(Boolean);

      // 创建字段类型映射表，用于选择字段时使用正确的类型
      const typeMapObj: Record<string, string> = {};
      data.fields.forEach((field: any) => {
        if (field.name && field.type) {
          typeMapObj[field.name] = field.type;
        }
      });

      // 保存字段类型映射以供字段选择使用
      fieldTypeMap.value = typeMapObj;

      console.log(`[OriginalColumnsTab] 成功获取到 ${availableFields.value.length} 个字段`);
    } else {
      console.log(`[OriginalColumnsTab] 未找到fields字段:`, data);
      // 尝试不同的路径
      if (data && data.data && data.data.fields && Array.isArray(data.data.fields)) {
        availableFields.value = data.data.fields.map((field: any) => field.name).filter(Boolean);
        const typeMapObj: Record<string, string> = {};
        data.data.fields.forEach((field: any) => {
          if (field.name && field.type) {
            typeMapObj[field.name] = field.type;
          }
        });
        fieldTypeMap.value = typeMapObj;
        console.log(`[OriginalColumnsTab] 在data.data中找到字段: ${availableFields.value.length} 个字段`);
      }
    }
  } catch (error) {
    console.error('[OriginalColumnsTab] 加载字段选项失败', error);
    message.error(`获取字段失败: ${error instanceof Error ? error.message : '无法从服务器获取字段信息'}`, 5000);
  } finally {
    // 可以在这里添加finally块的逻辑，如果需要的话
  }
};

// 字段下拉框相关方法
const openFieldDropdown = (index: number) => {
  activeFieldDropdown.value = index;

  // 在下一个渲染周期处理下拉菜单定位
  nextTick(() => {
    // 聚焦搜索输入框
    if (fieldSearchInput.value) {
      fieldSearchInput.value.focus();
    }
  });
};

const closeFieldDropdown = () => {
  activeFieldDropdown.value = -1;
  fieldSearchText.value = '';

  // 移除所有单元格的高亮类
  document.querySelectorAll('.field-dropdown-active').forEach(el => {
    el.classList.remove('field-dropdown-active');
  });
};

const toggleFieldDropdown = (column: TableColumn, index: number, evt?: Event) => {
  if (!column.isNewColumn) {
    return;
  }

  if (!availableFields || !availableFields.value || availableFields.value.length === 0) {
    loadFieldOptions();
  }

  // 先移除所有其他单元格的高亮类
  document.querySelectorAll('.field-dropdown-active').forEach(el => {
    el.classList.remove('field-dropdown-active');
  });

  document.querySelectorAll('.field-selector.active').forEach(el => {
    el.classList.remove('active');
  });

  if (activeFieldDropdown.value === index) {
    closeFieldDropdown();
  } else {
    // 找到当前单元格和字段选择器
    // 1. 获取事件触发的实际字段选择器元素
    const selector = evt?.target ? (evt.target as HTMLElement).closest('.field-selector') : null;
    // 2. 如果有事件目标，找到其所在的单元格
    const currentTd = selector ? selector.closest('td') : document.querySelector(`td:nth-child(${index + 2})`);

    if (currentTd) {
      currentTd.classList.add('field-dropdown-active');
      // 同时设置选择器的active类
      const fieldSelector = currentTd.querySelector('.field-selector');
      if (fieldSelector) {
        fieldSelector.classList.add('active');
      }
    }

    openFieldDropdown(index);
  }
};

// format变化时自动更新displayType
const handleFormatChange = (column: TableColumn) => {
  // 确保 column.format 不为 undefined
  let format = column.format || 'string';

  // 对于number类型，确保format为'int'或'decimal'
  if (column.type === 'number') {
    if (format !== 'int' && format !== 'decimal') {
      if (format.includes('int') || format === 'integer' || format === 'number') {
        format = 'int';
        column.format = format;
        console.log(`[OriginalColumnsTab] 修正整数列format: ${column.field}, format=${format}`);
      } else if (format.includes('decimal') || format.includes('float') || format.includes('double')) {
        format = 'decimal';
        column.format = format;
        console.log(`[OriginalColumnsTab] 修正小数列format: ${column.field}, format=${format}`);
      } else {
        // 如果没有明确的类型信息，默认设置为int
        format = 'int';
        column.format = format;
        console.log(`[OriginalColumnsTab] 未识别的数字类型列format: ${column.field}, 默认设置为int`);
      }
    }
  }

  // 基于format确定displayType
  const formatToDisplayTypeMap: Record<string, string> = {
    'string': 'text',
    'int': 'number',
    'decimal': 'number',
    'enum': 'tag',
    'date': 'date',
    'date-time': 'date',
    'card': 'sensitivity',
    'mobile': 'sensitivity',
    'uri': 'link',
    'email': 'sensitivity',
    'name': 'sensitivity',
    'address': 'sensitivity',
    'fixPhone': 'sensitivity',
    'CVV': 'sensitivity',
    'idCard': 'sensitivity',
    'json': 'text',
    'boolean': 'status'
  };

  // 根据格式设置托码类型
  const formatToMaskTypeMap: Record<string, string> = {
    'card': 'bankCard',
    'mobile': 'phone',
    'email': 'email',
    'name': 'name',
    'address': 'address',
    'fixPhone': 'fixPhone',
    'CVV': 'CVV',
    'idCard': 'idCard'
  };

  const recommendedDisplayType = formatToDisplayTypeMap[format] || 'text';
  column.displayType = recommendedDisplayType as DisplayType;
  console.log(`[数据格式变更] 根据format自动设置columnType: format=${format}, columnType=${column.displayType}`);

  // 设置托码类型
  if (formatToMaskTypeMap[format]) {
    column.maskType = formatToMaskTypeMap[format];
    console.log(`[数据格式变更] 设置托码类型: format=${format}, maskType=${column.maskType}`);
  }

  // 根据格式设置数字相关配置
  if (format === 'int' || format === 'decimal') {
    // 确保config对象存在
    if (!column.config) {
      column.config = {};
    }

    // 设置数字类型默认配置
    column.config.fixedPoint = format === 'decimal' ? "2" : "0";
    column.config.thousandSeparator = true;

    // 同步到顶级属性，以便在UI中显示
    (column as any).fixedPoint = column.config.fixedPoint;
    (column as any).thousandSeparator = column.config.thousandSeparator;

    console.log(`[数据格式变更] 设置数字格式配置: format=${format}, fixedPoint=${column.config.fixedPoint}, thousandSeparator=${column.config.thousandSeparator}`);
  }

  // 使用工具函数设置对齐方式
  column.align = convertToColumnAlign(getDefaultAlign(format));
};

// 使用utils/typeMapping.ts中的getDefaultAlign函数，并转换为ColumnAlign类型
const convertToColumnAlign = (align: string): ColumnAlign => {
  switch (align) {
    case 'right': return ColumnAlign.RIGHT;
    case 'center': return ColumnAlign.CENTER;
    case 'left':
    default: return ColumnAlign.LEFT;
  }
};

// 字段选择相关方法
const selectField = (column: TableColumn, field: string) => {
  // 从字段类型映射中获取该字段的类型
  const fieldType = fieldTypeMap.value[field] || 'string';

  // 根据字段类型设置列的数据类型（确保在UI上显示正确的类型）
  column.type = fieldType;

  // 获取对应的数据格式
  const format = getDefaultFormat(fieldType);

  // 根据format设置掩码类型
  const formatToMaskTypeMap: Record<string, string> = {
    'card': 'bankCard',
    'mobile': 'phone',
    'email': 'email',
    'name': 'name',
    'address': 'address',
    'fixPhone': 'fixPhone',
    'CVV': 'CVV',
    'idCard': 'idCard'
  };

  // 根据format设置掩码类型
  if (formatToMaskTypeMap[format]) {
    column.maskType = formatToMaskTypeMap[format];
    console.log(`[选择字段] 设置托码类型: format=${format}, maskType=${column.maskType}`);
  }

  // 获取显示类型（确保是小写的正确类型）
  let displayType: DisplayType = 'text';

  // 根据字段类型映射到正确的显示类型
  if (fieldType.toLowerCase().includes('int') ||
      fieldType.toLowerCase().includes('float') ||
      fieldType.toLowerCase().includes('decimal') ||
      fieldType.toLowerCase().includes('number')) {
    displayType = 'number';
  } else if (fieldType.toLowerCase().includes('date')) {
    displayType = 'date';
  } else if (fieldType.toLowerCase().includes('bool')) {
    displayType = 'boolean';
  } else if (fieldType.toLowerCase().includes('image')) {
    displayType = 'image';
  } else if (fieldType.toLowerCase().includes('link') ||
            fieldType.toLowerCase().includes('url')) {
    displayType = 'link';
  }

  // 更新列属性
  column.field = field;
  column.label = field;
  column.displayType = displayType;
  column.format = format; // 设置数据格式
  column.align = convertToColumnAlign(getDefaultAlign(fieldType));
  column.sortable = false; // 默认不允许排序
  column.filterable = false; // 默认不允许筛选

  // 根据格式设置数字相关配置
  if (format === 'int' || format === 'decimal') {
    // 确保config对象存在
    if (!column.config) {
      column.config = {};
    }

    // 设置数字类型默认配置
    column.config.fixedPoint = format === 'decimal' ? "2" : "0";
    column.config.thousandSeparator = true;

    // 同步到顶级属性，以便在UI中显示
    (column as any).fixedPoint = column.config.fixedPoint;
    (column as any).thousandSeparator = column.config.thousandSeparator;

    console.log(`[选择字段] 设置数字格式配置: format=${format}, fixedPoint=${column.config.fixedPoint}, thousandSeparator=${column.config.thousandSeparator}`);
  }

  // 设置为非新列，防止用户手动修改字段和字段类型
  column.isNewColumn = false;

  console.log(`[选择字段] 字段: ${field}, 类型: ${fieldType}, 格式: ${format}, 显示类型: ${displayType}`);

  // 清空搜索文本
  fieldSearchText.value = '';

  // 重置激活状态并清除高亮类
  activeFieldDropdown.value = -1;

  // 移除所有单元格的高亮类
  document.querySelectorAll('.field-dropdown-active').forEach(el => {
    el.classList.remove('field-dropdown-active');
  });

  // 触发重新渲染以确保UI更新
  nextTick(() => {
    // 确保无残留UI状态
    closeFieldDropdown();
  });
};

// 添加列
const addColumn = () => {
  try {
    console.log('[addColumn] 开始添加新列');
    console.log('[addColumn] 添加前columns长度:', columns.value?.length);

    const type = 'string'; // 默认字符串类型
    const format = getDefaultFormat(type);

  // 根据format设置掩码类型
  const formatToMaskTypeMap: Record<string, string> = {
    'card': 'bankCard',
    'mobile': 'phone',
    'email': 'email',
    'name': 'name',
    'address': 'address',
    'fixPhone': 'fixPhone',
    'CVV': 'CVV',
    'idCard': 'idCard'
  };

  // 根据format设置掩码类型
  let maskType = undefined;
  if (formatToMaskTypeMap[format]) {
    maskType = formatToMaskTypeMap[format];
    console.log(`[添加列] 设置托码类型: format=${format}, maskType=${maskType}`);
  }

  // 根据类型确定默认配置值
  let helpText = '';
  let fixedPoint = undefined;
  let thousandSeparator = undefined;
  let truncate = undefined;

  // 根据格式判断设置不同的默认值
  if (format === 'int' || format === 'decimal') {
    // 数字类型默认配置
    fixedPoint = format === 'decimal' ? "2" : "0";
    thousandSeparator = true;
    truncate = false;
    console.log(`[添加列] 设置数字格式配置: format=${format}, fixedPoint=${fixedPoint}, thousandSeparator=${thousandSeparator}`);
  } else if (format === 'string') {
    // 文本类型默认配置
    truncate = false;
  }

  // 设置显示类型为小写的text
  const displayType: DisplayType = 'text';

  const newColumn: ExtendedTableColumn = {
    id: `col_${Date.now()}`,
    field: '',
    label: '',
    type: type,
    displayType: displayType, // 使用小写的显示类型
    width: getDefaultWidth(type),
    align: convertToColumnAlign(getDefaultAlign(type)),
    visible: true,
    sortable: false, // 默认不允许排序
    filterable: false, // 默认不允许筛选
    displayOrder: columns.value.length,
    format: format, // 根据类型设置默认的数据格式
    maskType: maskType, // 添加掩码类型
    isNewColumn: true
    // helpText 属性已移除，因为它不在 TableColumn 类型中
  };

  // 添加config对象
  (newColumn as any).config = {
    help: helpText,
    fixedPoint: fixedPoint,
    thousandSeparator: thousandSeparator,
    truncate: truncate
  };

  // 创建新数组并添加列，而不是直接修改原数组
  const newColumns: ExtendedTableColumn[] = [...(columns.value as ExtendedTableColumn[]), newColumn];
  columns.value = newColumns; // 设置新值触发响应式更新

  console.log('[添加列] 添加后columns长度:', columns.value?.length);
  console.log('[添加列] 已添加新列:', newColumn);
  console.log('[添加列] 已触发update:columns事件');

  // 强制刷新UI
  nextTick(() => {
    console.log('[添加列] nextTick中确认columns更新', columns.value?.length);
  });

  message.success(`已添加新列配置`);
  } catch (err: any) {
    console.error('[添加列] 错误:', err);
    message.error(`添加列时发生错误: ${err.message}`);
  }
};

// 编辑列
const editColumn = (column: ExtendedTableColumn) => {
  // 确保使用内存中的最新数据
  const index = columns.value.findIndex((c: ExtendedTableColumn) => c.field === column.field);
  if (index !== -1) {
    // 使用内存中的列数据，而不是传入的参数
    column = columns.value[index];
  }

  // 确保config对象存在
  if (!column.config) {
    column.config = {};
  }

  // 是否已有保存的帮助信息，保留供调试用
  console.log('[editColumn] 开始编辑，原始列对象的config:', column.config);

  // 触发列编辑事件，通知父组件打开高级设置对话框
  // 创建完整的深拷贝确保所有config属性都被正确传递
  const deepCopyColumn = JSON.parse(JSON.stringify(column));
  console.log('[OriginalColumnsTab] 触发edit-column事件, 列配置:', deepCopyColumn);

  // 这里再次检查帮助文本是否在深拷贝中被正确保留
  if (deepCopyColumn.config) {
    console.log('[OriginalColumnsTab] 发送给父组件的config.help:', deepCopyColumn.config.help);
  }

  emit('edit-column', deepCopyColumn);

  // 保留原始功能以应对高级设置弹窗不可用的情况
  // 重要：拷贝列数据前，先确保config和help存在
  if (!column.config) {
    column.config = {};
  }

  console.log('[editColumn] 列数据深拷贝前的状态：', column);

  // 创建完整的深拷贝
  editingColumn.value = JSON.parse(JSON.stringify(column));

  // 直接强制设置helpText为config.help的值，即使是空字符串
  if (editingColumn.value.config) {
    // 将config.help（如果存在）直接赋值给helpText，否则给空字符串
    editingColumn.value.helpText = editingColumn.value.config.help !== undefined ? editingColumn.value.config.help : '';
      // 注意：helpText可能是空字符串，这是允许的合法值
  }

  // 将config对象中的属性同步到顶级属性，以便编辑界面能直接访问它们
  syncConfigToTopLevel(editingColumn.value);

  // 始终强制设置helpText，不管config中是什么值
  if (editingColumn.value.config) {
    // 永远强制设置，即使是空字符串
    editingColumn.value.helpText = editingColumn.value.config.help !== undefined ? editingColumn.value.config.help : '';
    console.log('[editColumn] 强制设置helpText值：', JSON.stringify(editingColumn.value.helpText));
  } else {
    // 如果config不存在，给它设置一个默认的空字符串
    editingColumn.value.helpText = '';
    console.log('[editColumn] config不存在，设置默认空字符串');
  }

  // 打印同步后的状态，用于检查help等值是否已正确同步到顶级
  console.log('[editColumn] 同步到顶级属性后的完整列对象:', JSON.parse(JSON.stringify(editingColumn.value)));

  // 特别检查config和helpText是否正确
  if (editingColumn.value.config) {
    console.log('[editColumn] config.help:', editingColumn.value.config.help);
    console.log('[editColumn] 列的helpText属性:', editingColumn.value.helpText);
  }

  showColumnEditModal.value = true;
};

// 关闭编辑弹窗
const closeColumnEditModal = () => {
  showColumnEditModal.value = false;
  showParamConfigModal.value = false; // 同时关闭参数配置弹窗
};

// 保存列编辑
const saveColumnEdit = () => {
  if (!editingColumn.value) return;

  try {
    console.log('[saveColumnEdit] 保存前列配置:', editingColumn.value);

    // 在保存前，将顶级属性同步回config对象
    syncTopLevelToConfig(editingColumn.value);

    // 打印当前列配置的深拷贝，用于对比
    console.log('[saveColumnEdit] 保存诞启多秒之后的深拷贝列配置:', JSON.parse(JSON.stringify(editingColumn.value)));

    console.log('[saveColumnEdit] 同步顶级属性回config后:', editingColumn.value);

    // 完整的深拷贝 - 使用JSON序列化确保所有嵌套对象也被复制
    const editedColumnCopy = JSON.parse(JSON.stringify(editingColumn.value));

    // 确保config对象存在并包含所有必要属性
    if (!editedColumnCopy.config) {
      editedColumnCopy.config = {};
    }

    // 手动确保将UI上的值复制到配置中 - 始终设置帮助文本，即使为空字符串
    // 使用=== undefined判断确保空字符串也能正确保存
    editedColumnCopy.config.help = editedColumnCopy.helpText === undefined ? '' : editedColumnCopy.helpText;

    if (editedColumnCopy.fixedPoint !== undefined) {
      editedColumnCopy.config.fixedPoint = editedColumnCopy.fixedPoint;
    }

    if (editedColumnCopy.thousandSeparator !== undefined) {
      editedColumnCopy.config.thousandSeparator = editedColumnCopy.thousandSeparator;
    }

    if (editedColumnCopy.truncate !== undefined) {
      editedColumnCopy.config.truncate = editedColumnCopy.truncate;
    }

    // 找到对应的列并更新
    const index = columns.value.findIndex((c: ExtendedTableColumn) => c.field === editedColumnCopy.field);
    if (index !== -1) {
      // 将完整深拷贝的编辑后列应用回表格配置
      columns.value[index] = editedColumnCopy;
      console.log('[saveColumnEdit] 更新后的列配置:', editedColumnCopy);
      message.success(`列配置已更新: 列 "${editedColumnCopy.label || editedColumnCopy.field}" 配置已保存`);
    }
  } catch (err: any) {
    console.error('[saveColumnEdit] 保存列配置出错:', err);
    message.error(`保存列配置失败: ${err.message}`);
  } finally {
    closeColumnEditModal();
  }
};

// 移除列
const removeColumn = (index: number) => {
  try {
    console.log('[removeColumn] 开始删除列, 索引:', index);
    console.log('[removeColumn] 删除前columns长度:', columns.value?.length);

    // 保存将被删除的字段名，用于调试
    const fieldToRemove = columns.value[index]?.field;
    console.log('[removeColumn] 将删除字段:', fieldToRemove);

    // 创建一份列数组的新副本，而不是直接修改原数组
    const newColumns: ExtendedTableColumn[] = [...(columns.value as ExtendedTableColumn[])];
    newColumns.splice(index, 1);

    // 更新columns值，触发响应式更新
    columns.value = newColumns;

    console.log('[removeColumn] 删除后columns长度:', columns.value?.length);
    console.log('[removeColumn] 已触发update:columns事件');

    // 强制刷新UI并手动检查按钮状态
    nextTick(() => {
      console.log('[removeColumn] nextTick中确认columns更新', columns.value?.length);

      // 手动记录当前已使用的字段
      const usedFields = new Set(columns.value.map((col: TableColumn) => col.field).filter(Boolean));

      // 检查可用字段
      console.log('[removeColumn] 可用字段数量:', availableFields.value?.length);

      // 检查是否还有未使用的字段
      const unusedFieldsExist = availableFields.value?.some(field => !usedFields.has(field));

      console.log('[removeColumn] 添加按钮状态检查: 未使用字段存在?', unusedFieldsExist);
      console.log('[removeColumn] 未使用字段列表:', availableFields.value?.filter(field => !usedFields.has(field)));

      // 如果availableFields为空，尝试重新加载
      if (!availableFields.value || availableFields.value.length === 0) {
        console.log('[removeColumn] 尝试重新加载字段选项');
        loadFieldOptions();
      }
    });

    message.success(`已删除列配置`);
  } catch (err: any) {
    console.error('[removeColumn] 错误:', err);
    message.error(`删除列时发生错误: ${err.message}`);
  }
};

// 处理拖拽变化
const onDragChange = (evt: any) => {
  // 更新排序顺序
  columns.value.forEach((column: ExtendedTableColumn, index: number) => {
    column.displayOrder = index;
  });
  message.success(`表格中列显示顺序已更新: 共 ${columns.value.length} 个列的显示顺序已更新`, 3000);
};

// 枚举选择器相关
const openEnumSelector = () => {
  if (editingColumn.value) {
    showEnumSelector.value = true;
  }
};

// 处理枚举选择
const handleEnumSelected = (result: { options: Array<{label: string, value: string}>; enumId: string; enumName: string; enumCode: string; }) => {
  if (editingColumn.value && result) {
    console.log('[OriginalColumnsTab] 选择枚举:', result);
    editingColumn.value.enumCode = result.enumCode || '';
    editingColumn.value.enumId = result.enumId;
    editingColumn.value.enumName = result.enumName;

    // 设置默认展示方式为文本
    if (!editingColumn.value.enumDisplay) {
      editingColumn.value.enumDisplay = 'text';
    }

    // 如果有选项，设置默认选中第一个
    if (result.options && result.options.length > 0) {
      editingColumn.value.defaultEnumValue = result.options[0].value;
    }

    // 使用丰富的消息通知
    const optionCount = result.options?.length || 0;
    message.success(`列已关联到枚举: 列 "${editingColumn.value.label || editingColumn.value.field}" 已关联到枚举 "${result.enumName}"${optionCount > 0 ? `，包含 ${optionCount} 个选项` : ''}`, 3000);
  }
  showEnumSelector.value = false;
};

// 关闭枚举选择器
const closeEnumSelector = () => {
  showEnumSelector.value = false;
};

// 打开参数配置弹窗
const openParamConfigModal = () => {
  if (editingColumn.value && editingColumn.value.format === 'enum') {
    showParamConfigModal.value = true;
  }
};

// 关闭参数配置弹窗
const closeParamConfigModal = () => {
  showParamConfigModal.value = false;
};

// 处理ParamAdvancedConfig保存事件
const handleParamSave = (param: any) => {
  if (!editingColumn.value) return;

  // 保存之前的枚举码，用于判断是否有变化
  const previousEnumCode = editingColumn.value.enumCode;

  // 更新编辑中列的枚举信息
  editingColumn.value.enumId = param.enumId;
  editingColumn.value.enumName = param.enumName;
  editingColumn.value.enumCode = param.enumCode;
  editingColumn.value.enumDisplay = param.exportConfig?.config?.displayType === 'select' ? 'text' : param.enumDisplay || 'text';

  // 如果参数有默认值，更新列的默认枚举值
  if (param.defaultValue) {
    editingColumn.value.defaultEnumValue = param.defaultValue;
  }

  // 关闭参数配置弹窗
  closeParamConfigModal();

  // 使用丰富的消息通知
  if (previousEnumCode !== param.enumCode) {
    // 枚举有变更
    message.success(`枚举关联已更新: 列「${editingColumn.value.label || editingColumn.value.field}」已关联到枚举「${param.enumName || param.enumCode}」`, 3000);
  } else {
    // 其他配置变更
    message.success(`显示配置已更新: 列「${editingColumn.value.label || editingColumn.value.field}」的显示配置已更新`, 3000);
  }
};
</script>

<style>
:root {
  --form-input-height: 38px;
  --form-input-padding: 0.5rem 0.75rem;
  --form-border-color: #d1d5db;
  --form-focus-ring-color: rgba(79, 70, 229, 0.2);
  --form-focus-border-color: #6366f1;
}

.field-selector {
  cursor: pointer;
  user-select: none;
  position: relative;
  z-index: 10;
}

.field-selector.active {
  z-index: 1000 !important;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* 确保下拉菜单显示在最上层 */
.relative {
  position: relative;
  z-index: 10;
}

/* 激活状态下的相对容器提升层级 */
td:has(.field-selector.active) .relative,
td:focus-within .relative {
  z-index: 1000 !important;
}

/* 另一种不使用:has的方式提升层级 */
.relative:focus-within {
  z-index: 1000 !important;
}

/* 下拉菜单内容应该显示在最高层级 */
.absolute {
  z-index: 1001 !important;
  position: absolute !important;
  width: 100%;
  left: 0;
  top: 100%;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.max-h-60 {
  z-index: 1002 !important;
}

/* 确保搜索框在最上层 */
.field-search-input {
  z-index: 1003 !important;
  position: relative;
}

/* 保证表格本身不挡住下拉菜单 */
table {
  border-collapse: separate;
  border-spacing: 0;
}

td {
  position: relative;
}

.drag-handle {
  cursor: move;
}

/* 参数配置弹窗层样式 */
.param-config-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3500 !important; /* 确保比其他弹窗更高 */
}

/* 修复字段列样式 */
td div.relative {
  position: relative;
}

td div.relative > div.flex {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  min-height: 38px;
}

/* 移除:has选择器，改用类选择器提升兼容性 */
.field-dropdown-active,
.field-dropdown-active .relative {
  z-index: 1000 !important;
  position: relative;
}

/* 字段选择器修复 */
.field-selector {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem;
  min-height: 38px;
  display: block;
  width: 100%;
  position: relative;
}

.field-selector:focus-within,
.field-selector:active {
  z-index: 100;
}

/* 全局表单元素样式修复 */
.form-input,
.form-select,
input[type='text'],
input[type='number'],
input:not([type]),
select {
  display: block;
  width: 100%;
  padding: var(--form-input-padding);
  font-size: 0.875rem;
  line-height: 1.25;
  color: #374151;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid var(--form-border-color);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus,
.form-select:focus,
input[type='text']:focus,
input[type='number']:focus,
input:not([type]):focus,
select:focus {
  border-color: var(--form-focus-border-color);
  outline: 0;
  box-shadow: 0 0 0 3px var(--form-focus-ring-color);
}

/* 确保下拉菜单有正确的样式 */
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: collapse;
}

table th {
  padding: 0.75rem;
  font-weight: 600;
  text-align: left;
  border-bottom: 1px solid var(--table-border-color, #e5e7eb);
  background-color: var(--table-header-bg, #f9fafb);
}

table td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--table-border-color, #e5e7eb);
  vertical-align: middle;
}

table tr:hover {
  background-color: var(--table-row-hover-bg, #f3f4f6);
}

/* 拖拽排序相关样式 */
.sortable-ghost {
  opacity: 0.5;
  background-color: var(--ghost-bg, #f1f5f9);
}

.sortable-drag {
  opacity: 0.9;
  background-color: var(--drag-bg, #ffffff);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.drag-handle {
  cursor: move;
  display: inline-flex;
  align-items: center;
  color: var(--handle-color, #9ca3af);
}

.drag-handle:hover {
  color: var(--handle-hover-color, #6b7280);
}

/* 按钮样式修复 */
button.text-indigo-600, button.text-red-600 {
  padding: 0.375rem 0.75rem;
  font-weight: 500;
}

button.inline-flex {
  padding: 0.5rem 1rem;
}
</style>
