<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onUnmounted } from '@/plugins/vue-types-fix'
import { useRouter } from 'vue-router'
import type { DataSource, DataSourceType, DataSourceStatus, TestConnectionParams } from '@/types/datasource'
import { DataSourceTypeEnum, DataSourceStatusEnum } from '@/types/datasource'
import { confirmModal } from '@/services/modal'
import { useDataSourceStore } from '@/stores/datasource'
// 导入通用分页组件
import Pagination from '@/components/common/Pagination.vue'

// 组件属性
const props = defineProps<{
  dataSources: DataSource[]
  loading?: boolean
  showActions?: boolean
  pagination?: {
    total: number
    page: number
    size: number
    totalPages: number
  }
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'refresh', params?: { page?: number, size?: number, name?: string, type?: DataSourceType, status?: DataSourceStatus }): void
  (e: 'edit', dataSource: DataSource): void
  (e: 'delete', dataSource: DataSource): void
  (e: 'view', dataSource: DataSource): void
  (e: 'select', dataSource: DataSource): void
  (e: 'add'): void
  (e: 'test-connection', params: TestConnectionParams, callback: (success: boolean) => void): void
  (e: 'sync-metadata', dataSource: DataSource): void
}>()

// 路由
const router = useRouter()

// 数据源存储
const dataSourceStore = useDataSourceStore()

// 跟踪每个数据源的下拉菜单状态
const menuState = ref<Record<string, boolean>>({})

// 记录菜单打开的位置
const menuPosition = ref<{id: string, top: number, left: number, width: number} | null>(null)

// 切换下拉菜单
const toggleMenu = (dataSourceId: string, event: Event) => {
  event.stopPropagation() // 阻止事件冒泡
  // 关闭所有其他菜单
  Object.keys(menuState.value).forEach(key => {
    if (key !== dataSourceId) {
      menuState.value[key] = false
    }
  })
  // 切换当前菜单
  menuState.value[dataSourceId] = !menuState.value[dataSourceId]

  // 获取按钮元素位置
  const button = event.currentTarget as HTMLElement
  const rect = button.getBoundingClientRect()

  // 定义菜单的宽度
  const menuWidth = 130; // 稍微缩小一点宽度

  // 记录菜单位置 - 使菜单右对齐按钮
  menuPosition.value = {
    id: dataSourceId,
    top: rect.bottom + window.scrollY + 2, // 往下偏移一点，增加一点间距
    left: rect.right - menuWidth + window.scrollX, // 实现右对齐
    width: menuWidth // 设置固定宽度
  }
}

// 关闭菜单
const closeMenu = (dataSourceId: string) => {
  menuState.value[dataSourceId] = false
}

// 计算属性：当前页码
const currentPage = computed(() => props.pagination?.page || 1)

// 计算属性：每页数量
const pageSize = computed(() => props.pagination?.size || 10)

// 计算分页号码显示
const pageNumbers = computed(() => {
  if (!props.pagination) return [1]

  const totalPages = props.pagination.totalPages
  const current = props.pagination.page
  const result = []

  if (totalPages <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)

    // 当前页靠近开始
    if (current <= 4) {
      result.push(2, 3, 4, 5)
      result.push('...')
      result.push(totalPages)
    }
    // 当前页靠近结束
    else if (current >= totalPages - 3) {
      result.push('...')
      result.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1)
      result.push(totalPages)
    }
    // 当前页在中间
    else {
      result.push('...')
      result.push(current - 1, current, current + 1)
      result.push('...')
      result.push(totalPages)
    }
  }

  return result
})

// 处理页码变化
const handlePageChange = (page: number) => {
  emit('refresh', {
    page,
    size: pageSize.value
  })
}

// 处理编辑数据源
const handleEdit = (dataSource: DataSource) => {
  emit('edit', dataSource)
}

// 处理删除数据源
const handleDelete = (dataSource: DataSource) => {
  confirmModal.confirm({
    title: '删除数据源',
    content: `确定要删除数据源"${dataSource.name}"吗？此操作不可恢复。`,
    okText: '删除',
    cancelText: '取消',
    okButtonType: 'danger',
    onOk: () => {
      emit('delete', dataSource)
    }
  })
}

// 处理查看数据源详情
const handleView = (dataSource: DataSource) => {
  emit('view', dataSource)
  emit('select', dataSource)
}

// 处理测试连接
const handleTestConnection = (dataSource: DataSource) => {
  const testParams: TestConnectionParams = {
    id: dataSource.id
  }

  emit('test-connection', testParams, (success: boolean) => {
    if (success) {
      // 处理连接成功的逻辑
      console.log('连接测试成功')
    } else {
      // 处理连接失败或取消的逻辑
      console.log('连接测试失败')
    }
  })
}

// 处理同步元数据
const handleSyncMetadata = (dataSource: DataSource) => {
  emit('sync-metadata', dataSource)
}

// 获取数据源类型显示
const getDataSourceTypeDisplay = (type: DataSourceType) => {
  const typeMap: Record<string, string> = {
    [DataSourceTypeEnum.MYSQL]: 'MySQL',
    [DataSourceTypeEnum.POSTGRESQL]: 'PostgreSQL',
    [DataSourceTypeEnum.ORACLE]: 'Oracle',
    [DataSourceTypeEnum.SQLSERVER]: 'SQL Server',
    [DataSourceTypeEnum.MONGODB]: 'MongoDB',
    [DataSourceTypeEnum.ELASTICSEARCH]: 'Elasticsearch'
  }
  return typeMap[type] || type
}

// 获取数据源状态显示
const getStatusDisplay = (status: DataSourceStatus) => {
  const statusMap: Record<string, { text: string }> = {
    [DataSourceStatusEnum.ACTIVE]: { text: '活跃' },
    [DataSourceStatusEnum.INACTIVE]: { text: '不活跃' },
    [DataSourceStatusEnum.ERROR]: { text: '错误' },
    [DataSourceStatusEnum.SYNCING]: { text: '同步中' }
  }
  return statusMap[status] || { text: status }
}

// 格式化日期时间
const formatDateTime = (dateTimeString: string) => {
  if (!dateTimeString) return '-'
  return dateTimeString
}

// 点击外部时关闭所有菜单
const handleOutsideClick = (event: MouseEvent) => {
  // 检查点击是否在菜单内或菜单按钮上
  const target = event.target as HTMLElement
  // 如果点击的是菜单按钮或其内部元素，不做处理
  if (target.closest('.menu-button') || target.closest('.dropdown-menu')) {
    return
  }
  // 关闭所有菜单
  menuState.value = {}
  menuPosition.value = null
}

onMounted(() => {
  document.addEventListener('click', handleOutsideClick)
})

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick)
})
</script>

<template>
  <div class="bg-white shadow rounded-lg overflow-hidden">
    <!-- 加载状态 -->
    <div v-if="loading" class="w-full py-12 flex items-center justify-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="dataSources.length === 0" class="w-full py-12 flex flex-col items-center justify-center">
      <div class="rounded-full bg-gray-100 h-16 w-16 flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-database text-gray-400 text-2xl"></i>
      </div>
      <h3 class="text-sm font-medium text-gray-900">暂无数据</h3>
      <p class="mt-1 text-sm text-gray-500">
        没有符合条件的数据源
        <span v-if="false">
          ，尝试清除筛选条件或使用其他搜索关键词
        </span>
      </p>
      <button
        v-if="showActions"
        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        @click="emit('add')"
      >
        <i class="fas fa-plus mr-2"></i>
        添加数据源
      </button>
    </div>

    <!-- 数据源列表 -->
    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              名称
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              类型
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              连接信息
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              上次同步
            </th>
            <th v-if="showActions" scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="dataSource in dataSources" :key="dataSource.id" class="hover:bg-gray-50">
            <!-- 名称列 -->
            <td class="px-6 py-4">
              <div class="flex items-start">
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ dataSource.name }}
                  </div>
                  <div class="text-sm text-gray-500 truncate max-w-xs" :title="dataSource.description">
                    {{ dataSource.description || '无描述' }}
                  </div>
                </div>
              </div>
            </td>

            <!-- 类型列 -->
            <td class="px-6 py-4">
              <div class="text-sm text-gray-900">
                {{ getDataSourceTypeDisplay(dataSource.type) }}
              </div>
            </td>

            <!-- 连接信息列 -->
            <td class="px-6 py-4">
              <div class="text-sm text-gray-900">
                {{ dataSource.host }}:{{ dataSource.port }}
              </div>
              <div class="text-sm text-gray-500">
                {{ dataSource.databaseName }}
              </div>
            </td>

            <!-- 状态列 -->
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"
                :class="{
                  'bg-green-100 text-green-800': dataSource.status === 'active',
                  'bg-gray-100 text-gray-800': dataSource.status === 'inactive',
                  'bg-red-100 text-red-800': dataSource.status === 'error',
                  'bg-blue-100 text-blue-800': dataSource.status === 'syncing'
                }"
              >
                {{ getStatusDisplay(dataSource.status).text }}
              </span>
              <div v-if="dataSource.errorMessage" class="text-xs text-red-500 mt-1 max-w-xs truncate" :title="dataSource.errorMessage">
                {{ dataSource.errorMessage }}
              </div>
            </td>

            <!-- 上次同步列 -->
            <td class="px-6 py-4">
              <div class="text-sm text-gray-900">
                {{ dataSource.lastSyncTime ? formatDateTime(dataSource.lastSyncTime) : '尚未同步' }}
              </div>
              <div class="text-xs text-gray-500">
                同步频率: {{ dataSource.syncFrequency === 'manual' ? '手动' :
                          dataSource.syncFrequency === 'hourly' ? '每小时' :
                          dataSource.syncFrequency === 'daily' ? '每天' :
                          dataSource.syncFrequency === 'weekly' ? '每周' : '每月' }}
              </div>
            </td>

            <!-- 操作列 -->
            <td v-if="showActions" class="px-6 py-4 text-right text-sm font-medium">
              <div class="relative">
                <div class="flex items-center space-x-2 justify-end">
                  <!-- 查看按钮 -->
                  <button
                    class="w-8 h-8 flex items-center justify-center rounded-full text-blue-600 hover:bg-blue-100"
                    @click="handleView(dataSource)"
                    title="查看"
                  >
                    <i class="fas fa-eye"></i>
                  </button>

                  <!-- 编辑按钮 -->
                  <button
                    class="w-8 h-8 flex items-center justify-center rounded-full text-green-600 hover:bg-green-100"
                    @click="handleEdit(dataSource)"
                    title="编辑"
                  >
                    <i class="fas fa-edit"></i>
                  </button>

                  <!-- 更多操作下拉菜单按钮 -->
                  <button
                    class="w-8 h-8 flex items-center justify-center rounded-full text-gray-600 hover:bg-gray-100 relative menu-button"
                    @click="(event) => toggleMenu(dataSource.id, event)"
                    title="更多操作"
                  >
                    <i class="fas fa-ellipsis-v"></i>

                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <Pagination v-if="pagination && pagination.total > 0"
      :current-page="currentPage"
      :total-items="pagination.total"
      :page-size="pageSize"
      @page-change="handlePageChange"
    />
  </div>

  <!-- 全局下拉菜单 - 使用fixed定位避免被父元素样式影响 -->
  <div
    v-if="menuPosition && menuState[menuPosition.id]"
    class="fixed rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 dropdown-menu"
    style="z-index: 9999 !important; display: block !important;"
    :style="{
      top: menuPosition.top + 'px',
      left: menuPosition.left + 'px',
      width: (menuPosition.width || 140) + 'px'
    }"
  >
    <div class="py-1 text-left" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
      <!-- 找到对应的数据源对象 -->
      <template v-if="menuPosition">
        <a
          href="#"
          class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
          role="menuitem"
          @click.prevent="handleTestConnection(dataSources.find((ds: DataSource) => ds.id === menuPosition?.id))"
        >
          <i class="fas fa-plug mr-2 text-indigo-600"></i> 测试连接
        </a>
        <a
          href="#"
          class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
          role="menuitem"
          @click.prevent="handleSyncMetadata(dataSources.find((ds: DataSource) => ds.id === menuPosition?.id))"
        >
          <i class="fas fa-sync-alt mr-2 text-blue-600"></i> 同步元数据
        </a>
        <a
          href="#"
          class="flex items-center px-3 py-2 text-sm text-red-600 hover:bg-gray-100"
          role="menuitem"
          @click.prevent="handleDelete(dataSources.find((ds: DataSource) => ds.id === menuPosition?.id))"
        >
          <i class="fas fa-trash-alt mr-2"></i> 删除
        </a>
      </template>
    </div>
  </div>
</template>