<script lang="ts" setup>
import {computed, onMounted, ref, watch} from 'vue';
import {useMessageService} from '@/services/message';
import {useResponseHandler} from '@/utils/api';
import instance from "@/utils/axios";

// 组件属性
const props = defineProps<{
  visible: boolean;
  type: 'datasource' | 'schema' | 'table' | 'column';
  itemId: string;
  itemName: string;
  initialIsAuthRequired?: boolean;
  initialIsEncrypted?: boolean;
  initialEncryptionType?: string;
  initialEncryptionConfig?: any;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'save', config: any): void;
}>();

// 消息服务
const messageService = useMessageService();
const {handleResponse} = useResponseHandler();

// 组件状态
const isLoading = ref(false);
const isAuthRequired = ref(false);
const isEncrypted = ref(false);
const encryptionType = ref('gm');
const aesKey = ref('');

// 标题计算属性
const modalTitle = computed(() => {
  const typeMap = {
    'datasource': '数据源',
    'schema': 'Schema',
    'table': '表',
    'column': '列字段'
  };
  return `${typeMap[props.type] || '项目'}授权配置`;
});

// 是否显示加密配置
const showEncryptionConfig = computed(() => {
  return props.type === 'column';
});

// 初始化表单数据
const initFormData = () => {
  console.log('初始化授权配置表单数据:', {
    initialIsAuthRequired: props.initialIsAuthRequired,
    initialIsEncrypted: props.initialIsEncrypted,
    initialEncryptionType: props.initialEncryptionType,
    initialEncryptionConfig: props.initialEncryptionConfig
  });

  // 设置授权状态 - 使用严格的类型检查
  isAuthRequired.value = props.initialIsAuthRequired === true;

  // 设置加密状态 - 使用严格的类型检查
  isEncrypted.value = props.initialIsEncrypted === true;

  // 设置加密类型
  encryptionType.value = props.initialEncryptionType || 'gm';

  // 设置AES密钥
  if (props.initialEncryptionConfig) {
    if (props.initialEncryptionConfig.aes) {
      encryptionType.value = 'aes';
      aesKey.value = props.initialEncryptionConfig.aes.aes_key || '';
    } else if (props.initialEncryptionConfig.gm) {
      encryptionType.value = 'gm';
    }
  }
};

// 监听props变化，重新初始化表单
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initFormData();
  }
}, {immediate: true});

// 监听各个初始值的变化
watch([
  () => props.initialIsAuthRequired,
  () => props.initialIsEncrypted,
  () => props.initialEncryptionType,
  () => props.initialEncryptionConfig
], () => {
  if (props.visible) {
    initFormData();
  }
});

// 初始化
onMounted(() => {
  initFormData();
});

// 保存配置
const saveConfig = async () => {
  isLoading.value = true;

  try {
    // 构建授权配置对象
    const authConfig: any = {
      id: props.itemId,
      authRequired: isAuthRequired.value,
      type: props.type.toUpperCase() // 转换为大写，符合后端要求
    };

    // 调用授权API
    const authResponse = await instance.put('/api/metadata/auth', authConfig);

    // 处理授权响应
    handleResponse(authResponse, {
      showSuccessMessage: false, // 不显示授权成功消息，等所有操作完成后再显示
      errorMessage: '授权配置保存失败'
    });

    // 如果是列字段且启用了加密，还需要更新加密配置
    if (props.type === 'column' && isEncrypted.value) {
      const encryptConfig: any = {};

      if (encryptionType.value === 'aes') {
        encryptConfig.aes = {
          aes_key: aesKey.value
        };
      } else if (encryptionType.value === 'gm') {
        encryptConfig.gm = {};
      }

      // 调用列字段配置API
      const columnResponse = await instance.put(`/api/metadata/columns/${props.itemId}/config`, encryptConfig);

      // 处理列字段配置响应
      handleResponse(columnResponse, {
        showSuccessMessage: false, // 不显示加密配置成功消息，等所有操作完成后再显示
        errorMessage: '加密配置保存失败'
      });
    }

    // 构建完整的配置对象，用于更新本地状态
    const config: any = {
      isAuthRequired: isAuthRequired.value
    };

    // 如果是列字段且启用了加密，添加加密配置
    if (props.type === 'column' && isEncrypted.value) {
      if (encryptionType.value === 'aes') {
        config.aes = {
          aes_key: aesKey.value
        };
      } else if (encryptionType.value === 'gm') {
        config.gm = {};
      }
    }

    // 显示成功消息
    messageService.success('配置保存成功');

    // 发送保存事件
    emit('save', config);

    // 关闭弹窗
    emit('close');
  } catch (error) {
    console.error('保存配置失败:', error);
    messageService.error('保存配置失败: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    isLoading.value = false;
  }
};

// 取消
const cancel = () => {
  emit('close');
};
</script>

<template>
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
      <!-- 弹窗标题 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ modalTitle }}</h3>
      </div>

      <!-- 弹窗内容 -->
      <div class="px-6 py-4">
        <!-- 授权配置 -->
        <div class="mb-4">
          <div class="flex items-center">
            <input
                id="isAuthRequired"
                v-model="isAuthRequired"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                type="checkbox"
            />
            <label class="ml-2 block text-sm text-gray-900" for="isAuthRequired">
              是否需要单独申请授权
            </label>
          </div>
          <p class="mt-1 text-sm text-gray-500">
            启用后，用户需要单独申请授权才能访问此{{ modalTitle.replace('授权配置', '') }}
          </p>
        </div>

        <!-- 加密配置（仅列字段显示） -->
        <div v-if="showEncryptionConfig" class="mt-6">
          <div class="flex items-center">
            <input
                id="isEncrypted"
                v-model="isEncrypted"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                type="checkbox"
            />
            <label class="ml-2 block text-sm text-gray-900" for="isEncrypted">
              是否需要解密
            </label>
          </div>
          <p class="mt-1 text-sm text-gray-500">
            启用后，系统将对此列字段进行解密处理
          </p>

          <!-- 解密算法配置 -->
          <div v-if="isEncrypted" class="mt-4 ml-6 p-4 bg-gray-50 rounded-md">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">解密算法</label>
              <div class="flex items-center space-x-4">
                <div class="flex items-center">
                  <input
                      id="gm"
                      v-model="encryptionType"
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      type="radio"
                      value="gm"
                  />
                  <label class="ml-2 text-sm text-gray-700" for="gm">国密</label>
                </div>
                <div class="flex items-center">
                  <input
                      id="aes"
                      v-model="encryptionType"
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      type="radio"
                      value="aes"
                  />
                  <label class="ml-2 text-sm text-gray-700" for="aes">AES</label>
                </div>
              </div>
            </div>

            <!-- AES配置 -->
            <div v-if="encryptionType === 'aes'" class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2" for="aesKey">AES密钥</label>
              <input
                  id="aesKey"
                  v-model="aesKey"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  placeholder="请输入AES密钥"
                  type="password"
              />
              <p class="mt-1 text-xs text-gray-500">请输入用于AES解密的密钥</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3 rounded-b-lg">
        <button
            :disabled="isLoading"
            class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            type="button"
            @click="cancel"
        >
          取消
        </button>
        <button
            :disabled="isLoading"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            type="button"
            @click="saveConfig"
        >
          <span v-if="isLoading" class="mr-2">
            <svg class="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24"
                 xmlns="http://www.w3.org/2000/svg">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    fill="currentColor"></path>
            </svg>
          </span>
          保存
        </button>
      </div>
    </div>
  </div>
</template>
