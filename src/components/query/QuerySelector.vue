<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
import { ref, computed, onMounted, watch } from 'vue';
import { useQueryStore } from '@/stores/query';
import { queryService } from '@/services/query';
import { message } from '@/services/message';
import type { Query, QueryHistoryParams, QueryType } from '@/types/query';

// 使用统一的消息服务

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  dataSourceId: {
    type: String,
    default: ''
  }
});

// 定义组件事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string | undefined): void
  (e: 'selected', id: string, query: Query): void
  (e: 'change', id: string | undefined): void
}>();

// 组件状态
const loading = ref(false);
const queries = ref<Query[]>([]);
const selectedQueryId = ref<string | undefined>(props.modelValue || '');
const searchValue = ref('');

// 计算属性：过滤后的查询列表作为选项
const queryOptions = computed(() => {
  let filtered = queries.value;
  
  // 按数据源过滤
  if (props.dataSourceId) {
    filtered = filtered.filter(query => query.dataSourceId === props.dataSourceId);
  }
  
  return filtered.map(query => ({
    value: query.id,
    label: query.name || `查询 ${query.id}`,
    disabled: false
  }));
});

// 处理选择变更
const handleChange = (value: string) => {
  console.log('[QuerySelector] 查询变更:', value);
  selectedQueryId.value = value;
  emit('update:modelValue', value);
  
  // 查找并发出选中的查询对象
  if (value) {
    const selectedQuery = queries.value.find(q => q.id === value);
    if (selectedQuery) {
      console.log('[QuerySelector] 选中查询:', selectedQuery);
      emit('selected', value, selectedQuery);
      emit('change', value);
    } else {
      console.warn(`[QuerySelector] 无法找到ID为${value}的查询`);
    }
  } else {
    emit('change', undefined);
  }
};

// 监听modelValue变化
watch(() => props.modelValue, (newValue: string | undefined) => {
  console.log(`[QuerySelector] modelValue变更: -> ${newValue}`);
  selectedQueryId.value = newValue || undefined;
  
  // 如果有新值但在当前查询列表中不存在，尝试加载查询
  if (newValue && queries.value.length > 0 && !queries.value.some(q => q.id === newValue)) {
    console.log(`[QuerySelector] 当前modelValue(${newValue})不在已加载的查询列表中，重新加载`);
    loadQueries();
  }
}, { immediate: true });

// 监听dataSourceId变化
watch(() => props.dataSourceId, (newValue, oldValue) => {
  console.log(`[QuerySelector] 数据源ID变更: ${oldValue} -> ${newValue}`);
  
  if (newValue !== oldValue) {
    // 重新加载查询列表
    loadQueries();
    
    // 如果当前选择的查询不属于新的数据源，则清空选择
    if (selectedQueryId.value) {
      const currentQuery = queries.value.find(q => q.id === selectedQueryId.value);
      if (currentQuery && currentQuery.dataSourceId !== newValue) {
        console.log(`[QuerySelector] 已选择的查询(${selectedQueryId.value})不属于新数据源(${newValue})，清空选择`);
        selectedQueryId.value = '';
        emit('update:modelValue', '');
        emit('change', undefined);
      }
    }
  }
}, { immediate: false });

// 生命周期钩子
onMounted(() => {
  console.log(`[QuerySelector] 组件挂载，初始modelValue: ${props.modelValue}`);
  loadQueries();
});

// 加载查询列表
const loadQueries = async () => {
  loading.value = true;
  console.log('[QuerySelector] 开始加载查询列表...');
  
  try {
    const params: QueryHistoryParams = { 
      page: 1, 
      size: 100,
      queryType: 'SQL' as QueryType 
    };
    
    // 如果指定了数据源ID，则在请求中包含该参数
    if (props.dataSourceId) {
      params.dataSourceId = props.dataSourceId;
      console.log(`[QuerySelector] 使用数据源ID(${props.dataSourceId})筛选查询`);
    }
    
    const result = await queryService.getQueries(params);
    
    console.log('[QuerySelector] 查询服务返回结果:', result);
    
    if (result && result.data) {
      // 确保我们正确处理API返回的分页结果，取出items
      const responseData = result.data;
      const queryItems = responseData.items || (Array.isArray(responseData) ? responseData : []);
      
      const oldLength = queries.value.length;
      queries.value = queryItems.map(query => {
        return {
          id: query.id,
          name: query.name || `查询 ${query.id}`,
          description: query.description,
          queryType: query.queryType || query.type,
          dataSourceId: query.dataSourceId
        };
      });
      
      console.log(`[QuerySelector] 成功加载${queries.value.length}个查询(原${oldLength}个)`);
      
      // 如果有指定数据源ID，记录过滤后的查询数量
      if (props.dataSourceId) {
        const filteredCount = queries.value.filter(q => q.dataSourceId === props.dataSourceId).length;
        console.log(`[QuerySelector] 按数据源ID(${props.dataSourceId})过滤后剩余${filteredCount}个查询`);
      }
    } else {
      console.warn('[QuerySelector] 查询服务返回空结果');
    }
  } catch (error) {
    console.error('[QuerySelector] 加载查询列表失败', error);
    message.error({
      content: '加载查询列表失败',
      description: '无法从服务器获取查询列表，请检查网络连接或稍后重试',
      duration: 5000
    });
  } finally {
    loading.value = false;
  }
};

// 刷新查询列表
const refreshQueries = async () => {
  await loadQueries();
};

// 过滤选项函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
</script>

<template>
  <div class="query-selector">
    <label 
      v-if="props.label" 
      :for="'query-selector-' + (Math.random().toString(36).substring(2))" 
      class="block text-sm font-medium text-gray-700 mb-1"
    >
      {{ props.label }}
      <span v-if="props.required" class="text-red-500">*</span>
    </label>
    
    <a-select
      v-model:value="selectedQueryId"
      :placeholder="props.placeholder || '请选择数据查询'"
      :disabled="props.disabled"
      :loading="loading"
      show-search
      :filter-option="filterOption"
      :status="props.error ? 'error' : undefined"
      style="width: 100%"
      @change="handleChange"
      option-filter-prop="label"
      :options="queryOptions"
    >
      <template #suffixIcon>
        <span class="suffix-icon-container">
          <i v-if="loading" class="fas fa-circle-notch fa-spin text-gray-400"></i>
          <a v-else @click.stop="refreshQueries" title="刷新查询列表">
            <i class="fas fa-sync-alt text-gray-400 hover:text-gray-600"></i>
          </a>
        </span>
      </template>
      <!-- 使用默认的a-select-option渲染方式，并自定义选项内容 -->
      <template #option="{ value, label, disabled }">
        <span class="flex items-center">
          <span 
            class="w-2 h-2 rounded-full mr-2"
            :class="{
              'bg-blue-500': !disabled,
              'bg-gray-400': disabled,
            }"
          ></span>
          {{ label }}
        </span>
      </template>
    </a-select>
    
    <div v-if="props.error" class="mt-1 text-sm text-red-600">
      {{ props.error }}
    </div>
  </div>
</template>

<style scoped>
.query-selector {
  position: relative;
}

.suffix-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 4px;
}

:deep(.ant-select-selection-search-input) {
  height: 100%;
}

:deep(.ant-select-selector) {
  height: 38px !important;
  padding: 4px 11px !important;
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-select-selection-item) {
  line-height: 30px !important;
}
</style>