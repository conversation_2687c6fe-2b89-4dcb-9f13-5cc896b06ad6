<script setup lang="ts">
import {computed, onUnmounted, ref, watch} from 'vue'
import type {QueryResult, QueryStatus} from '@/types/query'
import QueryVisualization from './QueryVisualization.vue'
import QueryAnalysis from './QueryAnalysis.vue'
import {message} from 'ant-design-vue'

// 定义组件属性
const props = defineProps<{
  results: QueryResult | null
  isLoading: boolean
  error: string | null
  queryId?: string // 添加queryId属性
}>()

// 定义组件事件
const emit = defineEmits<{
  (e: 'export-results', format: 'csv' | 'excel' | 'json'): void
  (e: 'apply-suggestion', query: string): void // 添加应用建议事件
  (e: 'cancel', queryId: string): void
}>()

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)
const pageSizeOptions = [10, 20, 50, 100]

// 是否显示表格列配置
const showColumnConfig = ref(false)

// 是否显示导出菜单
const showExportMenu = ref(false)

// 跟踪隐藏的列
const hiddenColumns = ref<string[]>([])

// 跟踪展示给用户的错误消息
const userFriendlyError = ref('')

// 当前激活的视图 (table、visualization 或 analysis)
const activeView = ref<'table' | 'visualization' | 'analysis'>('table')

// 查询开始运行时间计时器
const executionTime = ref(0)
let executionTimer: number | null = null

// 格式化运行时间
const formatRunningTime = (ms: number) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  if (minutes > 0) {
    return `${minutes}分钟 ${seconds % 60}秒`
  }
  return `${seconds}秒`
}

// 取消查询执行
const cancelQuery = () => {
  if (!props.queryId) return

  emit('cancel', props.queryId)
  stopTimer()
}

// 开始计时器
const startTimer = () => {
  if (executionTimer) return

  const startTime = Date.now()
  executionTimer = window.setInterval(() => {
    executionTime.value = Date.now() - startTime
  }, 1000)
}

// 停止计时器
const stopTimer = () => {
  if (executionTimer) {
    window.clearInterval(executionTimer)
    executionTimer = null
  }
  executionTime.value = 0
}

// 监听加载状态，开始或停止计时器
watch(() => props.isLoading, (newValue) => {
  if (newValue) {
    startTimer()
  } else {
    stopTimer()
  }
})

// 这里不需要调试函数的定义，已在下方定义

// 组件卸载时清理计时器
onUnmounted(() => {
  stopTimer()
})

// 计算属性：可见的列和列映射关系
const visibleColumnsAndMapping = computed(() => {
  if (!props.results) return { visibleColumns: [], columnMapping: {} }

  console.log('QueryResults计算可见列，results:', props.results)

  // 创建字段映射表
  const mapping: Record<string, any> = {}
  let columns: string[] = []

  // 处理后端返回的标准数据结构，包含data.data.fields数组格式
  if (props.results.data && props.results.data.data && props.results.data.data.fields && Array.isArray(props.results.data.data.fields)) {
    console.log('处理API返回的data.data.fields数据:', props.results.data.data.fields)

    props.results.data.data.fields.forEach(field => {
      if (typeof field === 'string') {
        columns.push(field)
        mapping[field] = { FIELD: field, LABEL: field }
      } else if (typeof field === 'object' && field !== null) {
        // 尝试获取字段名称，优先使用name，然后是FIELD，field，label等
        const fieldName = field.name || field.FIELD || field.field || field.label || field.LABEL ||
                         (field.type && field.type === 'String' && field.name ? field.name : '未知字段')
        columns.push(fieldName)
        mapping[fieldName] = {
          FIELD: fieldName,
          LABEL: field.label || field.LABEL || fieldName,
          TYPE: field.type || field.TYPE || 'string'
        }
      }
    })

    console.log('处理后的列:', columns)
    console.log('列映射:', mapping)

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 优先处理直接包含在根对象中的columns数组
  if (props.results.columns && Array.isArray(props.results.columns)) {
    console.log('使用直接的results.columns数据:', props.results.columns)

    props.results.columns.forEach(column => {
      if (typeof column === 'string') {
        columns.push(column)
        mapping[column] = { FIELD: column, LABEL: column }
      } else if (typeof column === 'object' && column !== null) {
        // 处理列对象格式
        const columnName = column.name || column.field || column.FIELD || column
        columns.push(columnName)
        mapping[columnName] = {
          FIELD: columnName,
          LABEL: column.label || column.LABEL || columnName,
          TYPE: column.type || column.TYPE || 'string'
        }
      }
    })

    console.log('处理后的列:', columns)
    console.log('列映射:', mapping)

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 处理后端返回的data.columns数组（这是API实际返回的格式）
  if (props.results.data && props.results.data.columns && Array.isArray(props.results.data.columns)) {
    console.log('处理API返回的data.columns数据:', props.results.data.columns)

    props.results.data.columns.forEach(column => {
      if (typeof column === 'string') {
        columns.push(column)
        mapping[column] = { FIELD: column, LABEL: column }
      } else if (typeof column === 'object' && column !== null) {
        // 处理列对象格式
        const columnName = column.name || column.field || column.FIELD || column
        columns.push(columnName)
        mapping[columnName] = {
          FIELD: columnName,
          LABEL: column.label || column.LABEL || columnName,
          TYPE: column.type || column.TYPE || 'string'
        }
      }
    })

    console.log('处理后的列:', columns)
    console.log('列映射:', mapping)

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 处理后端返回的标准数据结构，包含data.fields[{FIELD,LABEL等}]格式
  if (props.results.data && props.results.data.fields && Array.isArray(props.results.data.fields)) {
    console.log('处理API返回的data.fields数据:', props.results.data.fields)

    props.results.data.fields.forEach(field => {
      // 如果字段是字符串直接使用
      if (typeof field === 'string') {
        columns.push(field)
        mapping[field] = field
        return
      }

      // 如果包含FIELD属性的对象（后端特定格式）
      if (field.FIELD) {
        columns.push(field.FIELD)
        // 存储完整的字段信息
        mapping[field.FIELD] = field
        return
      }

      // 兼容其他可能的字段名格式
      const fieldName = field.name || field.field || field.label || field.LABEL || '未知字段'
      columns.push(fieldName)
      mapping[fieldName] = field
    })

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 处理API直接返回的fields (可能是对象数组或字符串数组)
  if (props.results.fields && Array.isArray(props.results.fields)) {
    console.log('处理API返回的fields数据:', props.results.fields)

    props.results.fields.forEach(field => {
      if (typeof field === 'string') {
        columns.push(field)
        mapping[field] = field
        return
      }

      if (field.FIELD) {
        columns.push(field.FIELD)
        mapping[field.FIELD] = field
        return
      }

      const fieldName = field.name || field.field || field.label || field.LABEL || '未知字段'
      columns.push(fieldName)
      mapping[fieldName] = field
    })

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 从第一行数据推断列名（即使columns字段为空数组）
  if (props.results.rows && Array.isArray(props.results.rows) && props.results.rows.length > 0) {
    console.log('从第一行数据推断列名 (rows有数据)')
    const firstRow = props.results.rows[0]
    if (typeof firstRow === 'object' && firstRow !== null) {
      columns = Object.keys(firstRow)

      columns.forEach(col => {
        mapping[col] = { FIELD: col, LABEL: col }
      })

      console.log('从rows推断的列:', columns)

      return {
        visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
        columnMapping: mapping
      }
    }
  }

  // 尝试从data.data.rows的第一行推断列名
  if (props.results.data && props.results.data.data && props.results.data.data.rows && Array.isArray(props.results.data.data.rows) && props.results.data.data.rows.length > 0) {
    console.log('从data.data.rows的第一行推断列名')
    const firstRow = props.results.data.data.rows[0]
    if (typeof firstRow === 'object' && firstRow !== null) {
      columns = Object.keys(firstRow)

      columns.forEach(col => {
        mapping[col] = { FIELD: col, LABEL: col }
      })

      return {
        visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
        columnMapping: mapping
      }
    }
  }

  // 如果都找不到，返回空数组
  console.warn('无法确定数据列，返回空数组')
  return { visibleColumns: [], columnMapping: {} }
})

// 从计算属性中解构出visibleColumns和columnMapping
const visibleColumns = computed(() => visibleColumnsAndMapping.value.visibleColumns)
const columnMapping = computed(() => visibleColumnsAndMapping.value.columnMapping)

// 计算属性：总页数
const totalPages = computed(() => {
  if (!props.results) return 1

  // 获取总记录数
  const count = totalRecords.value

  // 如果总记录数为0，则只有1页
  if (count === 0) return 1

  // 计算总页数（向上取整）
  return Math.ceil(count / pageSize.value)
})

// 计算属性：当前页的行数据
const paginatedRows = computed(() => {
  console.log('计算paginatedRows，currentPage:', currentPage.value, 'pageSize:', pageSize.value)

  // 如果没有结果数据，返回空数组
  if (!props.results) {
    console.log('没有结果数据')
    return []
  }

  let rows: any[] = []

  // 检查data.data.rows结构（与后端API响应匹配）
  if (props.results.data && props.results.data.data && props.results.data.data.rows) {
    console.log('使用data.data.rows数据')
    if (Array.isArray(props.results.data.data.rows)) {
      rows = props.results.data.data.rows
    } else if (typeof props.results.data.data.rows === 'object') {
      // 尝试将对象转为数组
      rows = Object.values(props.results.data.data.rows)
    }
  }
  // 检查data.rows结构
  else if (props.results.data && props.results.data.rows) {
    console.log('使用data.rows数据')
    if (Array.isArray(props.results.data.rows)) {
      rows = props.results.data.rows
    } else if (typeof props.results.data.rows === 'object') {
      // 尝试将对象转为数组
      rows = Object.values(props.results.data.rows)
    }
  }
  // 直接使用rows属性
  else if (props.results.rows) {
    console.log('使用直接的rows数据')
    if (Array.isArray(props.results.rows)) {
      rows = props.results.rows
    } else if (typeof props.results.rows === 'object') {
      // 尝试将对象转为数组
      rows = Object.values(props.results.rows)
    }
  }

  console.log('找到的行数据:', rows)

  // 如果rows不是数组或为空，返回空数组
  if (!Array.isArray(rows) || rows.length === 0) {
    console.warn('行数据不是数组或为空')
    return []
  }

  // 计算分页偏移量
  const start = (currentPage.value - 1) * pageSize.value
  const end = Math.min(start + pageSize.value, rows.length)

  console.log('分页计算: start=', start, 'end=', end, '总行数=', rows.length)

  // 返回当前页的数据
  return rows.slice(start, end)
})

// 计算总记录数
const totalRecords = computed(() => {
  if (!props.results) return 0

  // 检查data.data.rowCount
  if (props.results.data?.data?.rowCount !== undefined) {
    return props.results.data.data.rowCount
  }

  // 检查其他可能的位置
  if (props.results.data?.totalCount !== undefined) {
    return props.results.data.totalCount
  }
  if (props.results.rowCount !== undefined) {
    return props.results.rowCount
  }

  // 通过计算行数组长度获取
  if (props.results.data?.data?.rows && Array.isArray(props.results.data.data.rows)) {
    return props.results.data.data.rows.length
  }
  if (props.results.data?.rows && Array.isArray(props.results.data.rows)) {
    return props.results.data.rows.length
  }
  if (props.results.rows && Array.isArray(props.results.rows)) {
    return props.results.rows.length
  }

  return 0
})

// 调试函数：输出数据结构到控制台
const debugDataStructure = () => {
  if (!props.results) {
    console.log('没有结果数据可供调试')
    return
  }

  console.log('======= QueryResults 组件数据结构调试 =======')
  console.log('props.results:', props.results)

  try {
    // 打印完整的JSON结构
    console.log('完整的JSON结构:')
    console.log(JSON.stringify(props.results, null, 2))
  } catch (e) {
    console.log('无法将结果转为JSON，可能包含循环引用')
  }

  // 检查顶层结构
  console.log('结果包含data属性:', !!props.results.data)
  console.log('结果包含rows属性:', !!props.results.rows)
  console.log('结果包含columns属性:', !!props.results.columns)
  console.log('结果包含fields属性:', !!props.results.fields)

  // 检查data结构
  if (props.results.data) {
    console.log('data类型:', typeof props.results.data)
    console.log('data包含rows:', !!props.results.data.rows)
    console.log('data包含columns:', !!props.results.data.columns)
    console.log('data包含fields:', !!props.results.data.fields)
    console.log('data包含data:', !!props.results.data.data)

    // 检查data.data结构
    if (props.results.data.data) {
      console.log('data.data类型:', typeof props.results.data.data)
      console.log('data.data包含rows:', !!props.results.data.data.rows)
      console.log('data.data包含fields:', !!props.results.data.data.fields)

      // 检查data.data.rows
      if (props.results.data.data.rows) {
        console.log('data.data.rows类型:', typeof props.results.data.data.rows)
        console.log('data.data.rows是否为数组:', Array.isArray(props.results.data.data.rows))
        if (Array.isArray(props.results.data.data.rows)) {
          console.log('data.data.rows长度:', props.results.data.data.rows.length)
          if (props.results.data.data.rows.length > 0) {
            console.log('data.data.rows第一项:', props.results.data.data.rows[0])
          }
        } else {
          console.log('data.data.rows键:', Object.keys(props.results.data.data.rows))
        }
      }

      // 检查data.data.fields
      if (props.results.data.data.fields) {
        console.log('data.data.fields类型:', typeof props.results.data.data.fields)
        console.log('data.data.fields是否为数组:', Array.isArray(props.results.data.data.fields))
        if (Array.isArray(props.results.data.data.fields)) {
          console.log('data.data.fields长度:', props.results.data.data.fields.length)
          if (props.results.data.data.fields.length > 0) {
            console.log('data.data.fields第一项:', props.results.data.data.fields[0])
          }
        }
      }
    }
  }

  // 检查计算属性
  console.log('visibleColumns值:', visibleColumns.value)
  console.log('visibleColumns长度:', visibleColumns.value.length)
  console.log('paginatedRows值:', paginatedRows.value)
  console.log('paginatedRows长度:', paginatedRows.value.length)
  console.log('totalRecords:', totalRecords.value)
  console.log('totalPages:', totalPages.value)

  // 显示在页面上
  message.info('查询结果数据结构已输出到控制台，请查看')

  console.log('======= 调试结束 =======')
}

// 在结果变化时调试数据结构 - 已合并到下方的watch函数

// 切换列可见性
const toggleColumnVisibility = (column: string) => {
  if (hiddenColumns.value.includes(column)) {
    hiddenColumns.value = hiddenColumns.value.filter(col => col !== column)
  } else {
    hiddenColumns.value.push(column)
  }
}

// 显示所有列
const showAllColumns = () => {
  hiddenColumns.value = []
}

// 切换到指定页
const goToPage = (page: number) => {
  if (page < 1 || page > totalPages.value) return
  currentPage.value = page
}

// 更新每页显示数量
const updatePageSize = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置为第一页
}

// 导出数据
const exportData = (format: 'csv' | 'excel' | 'json') => {
  emit('export-results', format)
}

// 处理优化建议应用
const handleApplySuggestion = (query: string) => {
  emit('apply-suggestion', query)
}

// 格式化执行时间
const formatExecutionTime = (ms: number | undefined): string => {
  if (!ms) return '0 ms'

  if (ms < 1000) {
    return `${ms} ms`
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(2)} 秒`
  } else {
    const minutes = Math.floor(ms / 60000)
    const seconds = ((ms % 60000) / 1000).toFixed(2)
    return `${minutes} 分 ${seconds} 秒`
  }
}

// 处理错误信息的展示
watch(() => props.error, (error) => {
  if (error) {
    // 检查是否是被取消的查询
    if (error.includes('取消') || error.toLowerCase().includes('cancel')) {
      userFriendlyError.value = '查询已被取消'
    }
    // 检查是否是SQL语法错误
    else if (error.includes('syntax error')) {
      userFriendlyError.value = 'SQL语法错误，请检查您的查询语句'
    }
    // 检查是否是表不存在错误
    else if (error.includes('table') && error.includes('not exist')) {
      userFriendlyError.value = '表不存在，请检查表名是否正确'
    }
    // 检查是否是字段不存在错误
    else if (error.includes('column') && error.includes('not exist')) {
      userFriendlyError.value = '字段不存在，请检查字段名是否正确'
    }
    // 默认错误信息
    else {
      userFriendlyError.value = error
    }
  } else {
    userFriendlyError.value = ''
  }
})

// 辅助函数：处理查询结果
const processQueryResult = (results: any) => {
  if (!results) return;

  console.log("处理查询结果数据")

  // 检查是否有rows数据
  if (results.rows && Array.isArray(results.rows) && results.rows.length > 0) {
    // 如果columns为空，但rows有数据，从rows提取列名
    if (!results.columns || (Array.isArray(results.columns) && results.columns.length === 0)) {
      const firstRow = results.rows[0];
      if (typeof firstRow === 'object' && firstRow !== null) {
        const extractedColumns = Object.keys(firstRow).map(key => {
          return {
            name: key,
            label: key,
            type: typeof firstRow[key]
          };
        });

        console.log("从rows提取列信息:", extractedColumns);
        results.columns = extractedColumns;
      }
    }
  }

  return results;
}

// 当结果改变时，处理数据并调试
watch(() => props.results, (newResults) => {
  console.log("Results changed in QueryResults:", newResults)
  if (newResults) {
    // 处理查询结果数据
    processQueryResult(newResults);

    // 打印完整的结果结构，帮助调试
    try {
      console.log("完整结果结构:", JSON.stringify(newResults, null, 2))
    } catch (e) {
      console.log("结果过大或包含循环引用，无法完整打印")
    }

    // 特殊处理：如果有rows数据但columns为空，确保能从rows提取列信息
    if (newResults.rows && Array.isArray(newResults.rows) && newResults.rows.length > 0 &&
        (!newResults.columns || (Array.isArray(newResults.columns) && newResults.columns.length === 0))) {
      console.log("检测到有rows数据但columns为空，从rows提取列信息")
      // 这里不需要操作，visibleColumnsAndMapping计算属性会处理这种情况
    }

    // 检查是否有嵌套的data.data结构
    if (newResults.data && newResults.data.data) {
      console.log("检测到嵌套data.data结构:", newResults.data.data)

      // 处理嵌套的data.data.rows
      if (newResults.data.data.rows) {
        console.log("data.data.rows:", typeof newResults.data.data.rows)
        if (!Array.isArray(newResults.data.data.rows) && typeof newResults.data.data.rows === 'object') {
          console.warn("data.data.rows不是数组，尝试转换为数组")
          try {
            const rows = []
            for (const key in newResults.data.data.rows) {
              if (Object.prototype.hasOwnProperty.call(newResults.data.data.rows, key)) {
                rows.push(newResults.data.data.rows[key])
              }
            }
            newResults.data.data.rows = rows
            console.log("转换后的data.data.rows:", newResults.data.data.rows)
          } catch (e) {
            console.error("转换data.data.rows失败:", e)
          }
        }
      }

      // 处理嵌套的data.data.fields
      if (newResults.data.data.fields) {
        console.log("data.data.fields:", typeof newResults.data.data.fields)
        if (!Array.isArray(newResults.data.data.fields) && typeof newResults.data.data.fields === 'object') {
          console.warn("data.data.fields不是数组，尝试转换为数组")
          try {
            const fields = []
            for (const key in newResults.data.data.fields) {
              if (Object.prototype.hasOwnProperty.call(newResults.data.data.fields, key)) {
                const field = newResults.data.data.fields[key]
                if (typeof field === 'object') {
                  field.name = field.name || key
                  fields.push(field)
                } else {
                  fields.push({ name: key, type: typeof field })
                }
              }
            }
            newResults.data.data.fields = fields
            console.log("转换后的data.data.fields:", newResults.data.data.fields)
          } catch (e) {
            console.error("转换data.data.fields失败:", e)
          }
        }
      }
    }

    // 检查是否有直接的rows和columns
    if (newResults.rows) {
      console.log("直接的rows:", newResults.rows)
      console.log("直接的rows类型:", typeof newResults.rows)
      console.log("直接的rows是否为数组:", Array.isArray(newResults.rows))
      console.log("直接的rows长度:", Array.isArray(newResults.rows) ? newResults.rows.length : '非数组')

      // 确保即使返回数据的格式不完全符合期望，组件也能尝试显示数据
      if (!Array.isArray(newResults.rows) && typeof newResults.rows === 'object') {
        console.warn("Query results rows is not an array. Attempting to convert to array format.")
        try {
          // 尝试将对象转换为数组
          const rows = [];
          if (newResults.rows && Object.keys(newResults.rows).length > 0) {
            for (const key in newResults.rows) {
              if (Object.prototype.hasOwnProperty.call(newResults.rows, key)) {
                rows.push(newResults.rows[key]);
              }
            }
            newResults.rows = rows;
            console.log("转换后的rows:", newResults.rows)
          }
        } catch (e) {
          console.error("Failed to convert rows to array format:", e);
        }
      }
    }

    if (newResults.columns) {
      console.log("直接的columns:", newResults.columns)
      console.log("直接的columns类型:", typeof newResults.columns)
      console.log("直接的columns是否为数组:", Array.isArray(newResults.columns))
      console.log("直接的columns长度:", Array.isArray(newResults.columns) ? newResults.columns.length : '非数组')
    }

    // 检查是否有data对象
    if (newResults.data) {
      console.log("data对象:", newResults.data)
      console.log("data.columns:", newResults.data.columns)
      console.log("data.rows:", newResults.data.rows)

      // 如果data.rows存在但不是数组，尝试转换
      if (newResults.data.rows && !Array.isArray(newResults.data.rows) && typeof newResults.data.rows === 'object') {
        console.warn("data.rows不是数组，尝试转换为数组格式")
        try {
          const rows = [];
          for (const key in newResults.data.rows) {
            if (Object.prototype.hasOwnProperty.call(newResults.data.rows, key)) {
              rows.push(newResults.data.rows[key]);
            }
          }
          newResults.data.rows = rows;
          console.log("转换后的data.rows:", newResults.data.rows)
        } catch (e) {
          console.error("转换data.rows失败:", e);
        }
      }
    }

    // 自动调用调试函数，确保能看到数据结构
    debugDataStructure();

    // 额外检查paginatedRows和visibleColumns
    console.log('paginatedRows计算结果:', paginatedRows.value)
    console.log('paginatedRows长度:', paginatedRows.value.length)
    console.log('visibleColumns:', visibleColumns.value)
    console.log('visibleColumns长度:', visibleColumns.value.length)
  }

  // 重置分页和列配置
  currentPage.value = 1
  hiddenColumns.value = []
}, { immediate: true }) // 添加immediate: true，确保组件创建时立即执行

// 获取单元格值的函数
const getCellValue = (row: any, columnName: string): any => {
  // 如果行对象为空，直接返回null
  if (!row) return null

  // 直接尝试使用列名
  if (row[columnName] !== undefined) {
    return row[columnName]
  }

  // 尝试使用小写列名
  const lowerColumnName = columnName.toLowerCase()
  if (row[lowerColumnName] !== undefined) {
    return row[lowerColumnName]
  }

  // 尝试使用大写列名
  const upperColumnName = columnName.toUpperCase()
  if (row[upperColumnName] !== undefined) {
    return row[upperColumnName]
  }

  // 如果是嵌套路径（如 "user.name"），尝试访问嵌套属性
  if (columnName.includes('.')) {
    const parts = columnName.split('.')
    let value = row
    for (const part of parts) {
      if (value === null || value === undefined) return null
      value = value[part]
    }
    return value
  }

  // 遍历对象所有键，执行不区分大小写的比较
  const rowKeys = Object.keys(row)
  for (const key of rowKeys) {
    if (key.toLowerCase() === lowerColumnName) {
      return row[key]
    }
  }

  // 尝试使用列映射中的FIELD属性
  if (columnMapping.value[columnName] && columnMapping.value[columnName].FIELD) {
    const fieldName = columnMapping.value[columnName].FIELD
    if (row[fieldName] !== undefined) {
      return row[fieldName]
    }
  }

  // 找不到时返回null
  return null
}

// 格式化单元格内容
const formatCellValue = (value: any): string => {
  // 处理null或undefined值
  if (value === null || value === undefined) {
    return 'NULL'
  }

  // 处理字符串类型的值
  if (typeof value === 'string') {
    // 检测是否是JSON字符串
    if ((value.startsWith('{') && value.endsWith('}')) ||
        (value.startsWith('[') && value.endsWith(']'))) {
      try {
        // 尝试解析JSON字符串
        const jsonObj = JSON.parse(value)
        return JSON.stringify(jsonObj)
      } catch (e) {
        // 解析失败返回原字符串
        return value
      }
    }

    // 检查空字符串
    if (value.trim() === '') {
      return '(空字符串)'
    }

    return value
  }

  // 处理对象类型的值
  if (typeof value === 'object' && value !== null) {
    // 处理日期对象
    if (value instanceof Date) {
      return value.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
    }

    try {
      // 尝试将对象转为JSON字符串
      return JSON.stringify(value)
    } catch (e) {
      return String(value)
    }
  }

  // 处理布尔值
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  // 其他情况直接转为字符串
  return String(value)
}

// 获取每种状态的显示信息
const getStatusInfo = (status: QueryStatus | undefined): { label: string; color: string } => {
  switch (status) {
    case 'COMPLETED':
      return { label: '已完成', color: 'text-green-600' }
    case 'RUNNING':
      return { label: '执行中', color: 'text-blue-600' }
    case 'FAILED':
      return { label: '失败', color: 'text-red-600' }
    case 'CANCELLED':
      return { label: '已取消', color: 'text-yellow-600' }
    default:
      return { label: '未知', color: 'text-gray-600' }
  }
}
</script>

<template>
  <div class="h-full flex flex-col bg-white">
    <!-- 标签选择器 -->
    <div class="flex border-b border-gray-200">
      <button
        @click="activeView = 'table'"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 focus:outline-none',
          activeView === 'table'
            ? 'border-indigo-500 text-indigo-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
        ]"
      >
        <i class="fas fa-table mr-2"></i>
        表格
      </button>

      <button
        @click="activeView = 'visualization'"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 focus:outline-none',
          activeView === 'visualization'
            ? 'border-indigo-500 text-indigo-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
        ]"
      >
        <i class="fas fa-chart-bar mr-2"></i>
        可视化
      </button>

      <button
        @click="activeView = 'analysis'"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 focus:outline-none',
          activeView === 'analysis'
            ? 'border-indigo-500 text-indigo-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
        ]"
      >
        <i class="fas fa-microscope mr-2"></i>
        分析
      </button>
    </div>

    <!-- 加载状态 - 等待获取结果 -->
    <div v-if="isLoading" class="flex-1 flex items-center justify-center">
      <div class="text-center text-gray-500">
        <div class="w-8 h-8 border-2 border-gray-300 border-t-indigo-500 rounded-full animate-spin mx-auto mb-2"></div>
        <p>准备展示结果...</p>
      </div>
    </div>
    <!-- 无结果 -->
    <div v-else-if="!results || ((!paginatedRows || paginatedRows.length === 0) && (!visibleColumns || visibleColumns.length === 0))" class="flex-1 flex items-center justify-center p-4">
      <div class="text-center text-gray-500">
        <i class="fas fa-database text-gray-400 text-3xl mb-2"></i>
        <p>没有查询结果</p>
        <p class="text-sm mt-1">请修改查询条件或选择其他数据</p>

        <!-- 调试按钮 - 仅输出到控制台 -->
        <div class="mt-4">
          <button
            @click="debugDataStructure"
            class="px-3 py-1 bg-blue-50 text-blue-600 border border-blue-200 rounded text-sm hover:bg-blue-100"
          >
            <i class="fas fa-bug mr-1"></i>
            在控制台查看数据结构
          </button>
        </div>

        <!-- 调试信息面板 -->
        <div v-if="results" class="mt-3 p-2 bg-gray-100 rounded text-xs text-left overflow-auto max-h-40 max-w-md">
          <p class="font-bold">调试信息：</p>
          <p>结果对象类型: {{ typeof results }}</p>
          <p>结果包含data: {{ results.data ? '是' : '否' }}</p>
          <p>结果包含data.data: {{ results.data && results.data.data ? '是' : '否' }}</p>
          <p>直接rows存在: {{ results.rows ? '是' : '否' }}</p>
          <p>直接rows长度: {{ results.rows && Array.isArray(results.rows) ? results.rows.length : '非数组或不存在' }}</p>
          <p v-if="results.data">data.rows存在: {{ results.data.rows ? '是' : '否' }}</p>
          <p v-if="results.data && results.data.rows">data.rows长度: {{ Array.isArray(results.data.rows) ? results.data.rows.length : '非数组' }}</p>
          <p v-if="results.data && results.data.data">data.data.rows存在: {{ results.data.data.rows ? '是' : '否' }}</p>
          <p v-if="results.data && results.data.data && results.data.data.rows">data.data.rows长度: {{ Array.isArray(results.data.data.rows) ? results.data.data.rows.length : '非数组' }}</p>
          <p v-if="results.data && results.data.data && results.data.data.fields">data.data.fields存在: 是</p>
          <p v-if="results.data && results.data.data && results.data.data.fields && Array.isArray(results.data.data.fields)">data.data.fields长度: {{ results.data.data.fields.length }}</p>
          <p v-if="results.data && results.data.data && results.data.data.fields && Array.isArray(results.data.data.fields) && results.data.data.fields.length > 0">第一个field示例: {{ JSON.stringify(results.data.data.fields[0]) }}</p>
          <p>分页数据长度: {{ paginatedRows ? paginatedRows.length : '无分页数据' }}</p>
          <p>可见列长度: {{ visibleColumns ? visibleColumns.length : '无可见列' }}</p>
          <p v-if="results.data && results.data.data && results.data.data.rows && !Array.isArray(results.data.data.rows)">
            data.data.rows类型: {{ typeof results.data.data.rows }}
          </p>
          <div class="mt-2">
            <button
              @click="debugDataStructure"
              class="w-full px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs"
            >
              查看完整数据结构
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- 查询结果内容 -->
    <div v-else class="flex-1 flex flex-col overflow-hidden">
      <!-- 表格视图 -->
      <div v-if="activeView === 'table'" class="flex-1 overflow-auto">
        <div class="flex justify-between items-center px-4 py-2 bg-gray-50 border-b">
          <div class="flex items-center">
            <span class="text-sm text-gray-600">共 {{ totalRecords }} 条结果</span>
            <span v-if="results.executionTime" class="ml-4 text-sm text-gray-600">
              执行时间: {{ formatExecutionTime(results.executionTime) }}
            </span>
            <span v-if="results.status" class="ml-4 text-sm" :class="getStatusInfo(results.status).color">
              状态: {{ getStatusInfo(results.status).label }}
            </span>
          </div>

          <div class="flex items-center space-x-2">
            <!-- 列配置 -->
            <button
              @click="showColumnConfig = !showColumnConfig"
              class="p-1 text-gray-500 hover:text-indigo-600 rounded"
              :class="{'text-indigo-600': showColumnConfig}"
            >
              <i class="fas fa-columns"></i>
            </button>

            <!-- 导出按钮 -->
            <div class="relative">
              <button
                @click="showExportMenu = !showExportMenu"
                class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-50"
              >
                <i class="fas fa-download mr-1"></i>
                导出
              </button>
              <!-- 导出菜单 -->
              <div
                v-show="showExportMenu"
                class="absolute right-0 mt-2 bg-white shadow-lg rounded-md py-1 z-10 w-32"
              >
                <button @click="exportData('csv'); showExportMenu = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                  导出为 CSV
                </button>
                <button @click="exportData('excel'); showExportMenu = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                  导出为 Excel
                </button>
                <button @click="exportData('json'); showExportMenu = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                  导出为 JSON
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 列配置面板 -->
        <div v-if="showColumnConfig" class="p-3 bg-gray-50 border-b">
          <div class="flex flex-wrap gap-2">
            <div
              v-for="column in visibleColumns"
              :key="column"
              class="px-2 py-1 bg-white border rounded-full text-xs flex items-center"
              :class="hiddenColumns.includes(column) ? 'text-gray-400 border-gray-300' : 'text-gray-700 border-gray-400'"
            >
              <span class="mr-1">{{ columnMapping[column]?.LABEL || column }}</span>
              <button @click="toggleColumnVisibility(column)" class="ml-1">
                <i :class="hiddenColumns.includes(column) ? 'far fa-eye-slash' : 'far fa-eye'"></i>
              </button>
            </div>

            <button
              v-if="hiddenColumns.length > 0"
              @click="showAllColumns"
              class="px-2 py-1 bg-indigo-50 text-indigo-600 border border-indigo-200 rounded-full text-xs"
            >
              显示所有列
            </button>
          </div>
        </div>

        <!-- 表格 -->
        <div class="overflow-auto flex-1">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th
                  v-for="column in visibleColumns"
                  :key="column"
                  class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky top-0 bg-gray-50 border-b"
                >
                  {{ columnMapping[column]?.LABEL || column }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <!-- 没有分页数据的情况 -->
              <tr v-if="paginatedRows.length === 0">
                <td
                  :colspan="visibleColumns.length || 1"
                  class="px-4 py-4 text-center text-sm text-gray-500"
                >
                  <span v-if="isLoading">加载数据中...</span>
                  <span v-else>
                    没有符合条件的数据 (paginatedRows长度: {{ paginatedRows.length }}, visibleColumns长度: {{ visibleColumns.length }})
                    <button
                      @click="debugDataStructure"
                      class="ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded text-gray-700"
                    >
                      查看数据结构
                    </button>
                  </span>
                </td>
              </tr>

              <!-- 正常行数据渲染 -->
              <tr v-for="(row, rowIndex) in paginatedRows" :key="rowIndex" class="hover:bg-gray-50">
                <td
                  v-for="column in visibleColumns"
                  :key="column"
                  class="px-4 py-2 text-sm text-gray-700 max-w-xs truncate"
                  :title="formatCellValue(getCellValue(row, column))"
                >
                  {{ formatCellValue(getCellValue(row, column)) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="px-4 py-2 bg-white border-t flex justify-between items-center">
          <div class="flex items-center text-sm text-gray-600">
            <span>每页显示</span>
            <select
              v-model="pageSize"
              @change="(e: Event) => updatePageSize(Number((e.target as HTMLSelectElement).value))"
              class="mx-1 px-2 py-1 border rounded text-sm"
            >
              <option v-for="size in pageSizeOptions" :key="size" :value="size">{{ size }}</option>
            </select>
            <span>条</span>

            <!-- 添加结果总数显示 -->
            <span class="ml-4">
              共
              <span class="font-medium">
                {{ totalRecords }}
              </span>
              条记录
            </span>
          </div>

          <div class="flex items-center space-x-1">
            <button
              @click="goToPage(1)"
              :disabled="currentPage === 1"
              class="p-2 rounded hover:bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="p-2 rounded hover:bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <i class="fas fa-angle-left"></i>
            </button>

            <span class="text-sm text-gray-600">
              {{ currentPage }} / {{ totalPages }}
            </span>

            <button
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage >= totalPages"
              class="p-2 rounded hover:bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <i class="fas fa-angle-right"></i>
            </button>
            <button
              @click="goToPage(totalPages)"
              :disabled="currentPage >= totalPages"
              class="p-2 rounded hover:bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 可视化视图 -->
      <div v-else-if="activeView === 'visualization'" class="flex-1 overflow-auto">
        <QueryVisualization
          :query-id="queryId || ''"
          :query-result="results"
          :is-loading="isLoading"
        />
      </div>

      <!-- 分析视图 -->
      <div v-else-if="activeView === 'analysis'" class="flex-1 overflow-auto">
        <QueryAnalysis
          :query-id="queryId || ''"
          :data-source-id="results?.dataSourceId || ''"
          :parameters="results?.parameters || {}"
          :result-id="results?.id || ''"
          :sql="results?.sql || ''"
          @apply-suggestion="handleApplySuggestion"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.table-container {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
