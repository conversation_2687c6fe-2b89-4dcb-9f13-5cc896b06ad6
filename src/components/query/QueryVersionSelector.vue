<template>
  <div class="query-version-selector">
    <label v-if="label" class="block text-sm font-medium text-gray-700 mb-1">
      {{ label }} <span v-if="required" class="text-red-500">*</span>
    </label>
    <div class="relative">
      <a-select
        v-model:value="selectedVersionId"
        :placeholder="placeholder"
        :disabled="!queryId || loading || props.disabled"
        :status="error ? 'error' : undefined"
        style="width: 100%"
        :options="versionOptions"
        :loading="loading"
      >
        <template #suffixIcon>
          <span class="suffix-icon-container">
            <i v-if="loading" class="fas fa-circle-notch fa-spin text-gray-400"></i>
            <i v-else class="fas fa-code-branch text-gray-400"></i>
          </span>
        </template>
      </a-select>
    </div>
    <p v-if="error" class="mt-1 text-sm text-red-600">{{ error }}</p>
    <p v-if="queryId && !versionNumber && !loading" class="mt-1 text-sm text-yellow-600">
      <i class="fas fa-exclamation-triangle mr-1"></i> 未找到查询版本
    </p>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
import { ref, watch, onMounted, computed } from 'vue';
import { message } from '@/services/message';
import instance from '@/utils/axios';
import {getApiBaseUrl} from "@/services/query";

// 版本类型定义
interface QueryVersion {
  id: string;
  versionNumber: number;
  isLatest: boolean;
  isActive?: boolean;
  status?: string;
}

const props = defineProps({
  modelValue: {
    type: [String, Object],
    default: ''
  },
  queryId: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: '查询版本'
  },
  placeholder: {
    type: String,
    default: '请选择查询版本'
  },
  error: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'selected']);

// 消息提示服务已使用统一的message

// 本地状态
const versions = ref<QueryVersion[]>([]);
const loading = ref(false);
const selectedVersionId = ref(props.modelValue);
const versionNumber = ref('');

// 计算属性：处理版本选项
const versionOptions = computed(() => {
  // 只筛选状态为PUBLISHED的版本
  const publishedVersions = versions.value.filter(version => version.status === 'PUBLISHED');
  
  return publishedVersions.map(version => ({
    value: version.id,
    label: `版本 ${version.versionNumber}${version.isLatest ? ' (最新)' : ''}${version.isActive ? ' (已激活)' : ''}`,
    isLatest: version.isLatest,
    isActive: version.isActive
  }));
});

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue: any) => {
  console.log('QueryVersionSelector: modelValue 发生变化:', newValue);
  console.log('QueryVersionSelector: modelValue 类型:', typeof newValue);

  try {
    if (typeof newValue === 'object' && newValue !== null) {
      console.log('QueryVersionSelector: 接收到对象类型的版本信息:', newValue);
      console.log('QueryVersionSelector: currentVersion:', newValue.currentVersion);
      // 如果传入的是对象，直接使用其中的 versionNumber
      if (newValue.currentVersion && typeof newValue.currentVersion === 'object') {
        versionNumber.value = newValue.currentVersion?.versionNumber?.toString() || '';
        selectedVersionId.value = newValue.currentVersion?.id || '';
      } else {
        // 如果没有 currentVersion 或者不是对象，则使用默认值或ID
        versionNumber.value = '1';
        selectedVersionId.value = typeof newValue.id === 'string' ? newValue.id : '';
      }
      console.log('QueryVersionSelector: 设置版本号:', versionNumber.value);
      console.log('QueryVersionSelector: 设置版本ID:', selectedVersionId.value);
    } else {
      console.log('QueryVersionSelector: 接收到字符串类型的版本信息:', newValue);
      // 如果传入的是字符串，保持原有逻辑
      selectedVersionId.value = newValue;
      console.log('QueryVersionSelector: 设置版本ID:', selectedVersionId.value);
    }
  } catch (error) {
    console.error('QueryVersionSelector: 处理modelValue时出错:', error);
    // 发生错误时设置默认值
    versionNumber.value = '1';
    selectedVersionId.value = typeof newValue === 'string' ? newValue : '';
  }
}, { immediate: true });

// 监听 selectedVersionId 变化
watch(selectedVersionId, (newValue: string) => {
  console.log('QueryVersionSelector: selectedVersionId 发生变化:', newValue);
  emit('update:modelValue', newValue);
  const selectedVersion = versions.value.find(v => v.id === newValue);
  console.log('QueryVersionSelector: 找到的版本信息:', selectedVersion);
  if (selectedVersion) {
    emit('selected', newValue, selectedVersion);
    // 更新版本号显示
    versionNumber.value = selectedVersion.versionNumber.toString();
  }
});

// 监听 queryId 变化，重新加载版本
watch(() => props.queryId, (newValue: string, oldValue: string) => {
  console.log('QueryVersionSelector: queryId 发生变化:', { oldValue, newValue });
  if (newValue !== oldValue) {
    selectedVersionId.value = '';
    versionNumber.value = '';
    if (newValue) {
      loadVersions(newValue);
    } else {
      versions.value = [];
    }
  }
}, { immediate: true });

// 加载查询版本
const loadVersions = async (queryId: string) => {
  if (!queryId) return;

  loading.value = true;
  versions.value = [];

  try {
    console.log(`QueryVersionSelector: 开始加载查询${queryId}的版本列表...`);
    const response = await instance.get(`/api/queries/${queryId}/versions`);
    console.log('QueryVersionSelector: 获取版本列表响应:', response.data);

    // 判断响应是否包含预期的数据结构 - 不再检查success字段，而是检查是否有必要的分页结构
    if (response.data && response.data.data && response.data.data.items) {
      // 根据API返回格式获取版本列表 - 修改为支持分页格式的数据结构
      const versionsData = response.data.data.items || [];
      console.log('QueryVersionSelector: 获取到的版本数据:', versionsData);

      // 遍历版本数据
      versions.value = Array.isArray(versionsData)
        ? versionsData.map((v: any) => ({
            id: v.id,
            versionNumber: v.versionNumber || 1,
            isLatest: v.isLatest || false,
            isActive: v.isActive || false,
            status: v.status || 'DRAFT'
          }))
        : [];

      console.log(`QueryVersionSelector: 处理后的版本列表(${versions.value.length}个):`, versions.value);

      // 筛选出已发布的版本
      const publishedVersions = versions.value.filter(v => v.status === 'PUBLISHED');
      console.log(`QueryVersionSelector: 已发布版本(${publishedVersions.length}个):`, publishedVersions);

      if (publishedVersions.length === 0) {
        console.log('QueryVersionSelector: 未找到已发布的查询版本');
        message.info({
          content: '未找到已发布的查询版本',
          description: '当前查询没有已发布的版本，请联系管理员发布版本',
          duration: 5000
        });
      } else {
        // 预选中活跃版本
        const activeVersion = publishedVersions.find(v => v.isActive);
        console.log('QueryVersionSelector: 找到的活跃版本:', activeVersion);
        if (activeVersion) {
          selectedVersionId.value = activeVersion.id;
          versionNumber.value = activeVersion.versionNumber.toString();
          console.log(`QueryVersionSelector: 自动选择活跃版本:`, {
            id: activeVersion.id,
            versionNumber: versionNumber.value
          });
        } else if (publishedVersions.length > 0) {
          // 如果没有活跃版本，选中最新的已发布版本
          const latestVersion = publishedVersions.sort((a, b) => b.versionNumber - a.versionNumber)[0];
          selectedVersionId.value = latestVersion.id;
          versionNumber.value = latestVersion.versionNumber.toString();
          console.log(`QueryVersionSelector: 未找到活跃版本，选择最新的已发布版本:`, {
            id: latestVersion.id,
            versionNumber: versionNumber.value
          });
        }
      }
    } else if (response.data && response.data.items) {
      // 直接处理没有嵌套data字段的响应格式
      const versionsData = response.data.items || [];
      console.log('QueryVersionSelector: 获取到的版本数据(直接格式):', versionsData);

      versions.value = Array.isArray(versionsData)
        ? versionsData.map((v: any) => ({
            id: v.id,
            versionNumber: v.versionNumber || 1,
            isLatest: v.isLatest || false,
            isActive: v.isActive || false,
            status: v.status || 'DRAFT'
          }))
        : [];

      console.log(`QueryVersionSelector: 处理后的版本列表(${versions.value.length}个):`, versions.value);

      // 筛选出已发布的版本
      const publishedVersions = versions.value.filter(v => v.status === 'PUBLISHED');
      console.log(`QueryVersionSelector: 已发布版本(${publishedVersions.length}个):`, publishedVersions);

      if (publishedVersions.length === 0) {
        console.log('QueryVersionSelector: 未找到已发布的查询版本');
        message.info({
          content: '未找到已发布的查询版本',
          description: '当前查询没有已发布的版本，请联系管理员发布版本',
          duration: 5000
        });
      } else {
        // 预选中活跃版本或最新的已发布版本
        const activeVersion = publishedVersions.find(v => v.isActive);
        if (activeVersion) {
          selectedVersionId.value = activeVersion.id;
          versionNumber.value = activeVersion.versionNumber.toString();
          console.log(`QueryVersionSelector: 自动选择活跃版本:`, {
            id: activeVersion.id,
            versionNumber: versionNumber.value
          });
        } else if (publishedVersions.length > 0) {
          // 如果没有活跃版本，选择最新的已发布版本
          const latestVersion = publishedVersions.sort((a, b) => b.versionNumber - a.versionNumber)[0];
          selectedVersionId.value = latestVersion.id;
          versionNumber.value = latestVersion.versionNumber.toString();
          console.log(`QueryVersionSelector: 未找到活跃版本，选择最新的已发布版本:`, {
            id: latestVersion.id,
            versionNumber: versionNumber.value
          });
        }
      }
    } else {
      console.warn('QueryVersionSelector: API响应格式不符合预期:', response.data);
      message.warning({
        content: '获取查询版本失败',
        description: '服务器响应格式不符合预期，请重试或联系管理员',
        duration: 4000
      });
      versions.value = [];
    }
  } catch (error) {
    console.error('QueryVersionSelector: 加载查询版本失败:', error);
    message.error({
      content: '加载查询版本失败',
      description: '无法从服务器获取查询版本信息，请重试或联系系统管理员',
      duration: 5000
    });
    versions.value = [];
  } finally {
    loading.value = false;
    console.log('QueryVersionSelector: 版本加载完成，当前状态:', {
      versions: versions.value,
      selectedVersionId: selectedVersionId.value,
      versionNumber: versionNumber.value
    });
  }
};

// 加载初始版本数据
onMounted(() => {
  console.log('QueryVersionSelector: 组件挂载，初始状态:', {
    modelValue: props.modelValue,
    queryId: props.queryId,
    versionNumber: versionNumber.value
  });
  if (props.queryId) {
    loadVersions(props.queryId);
  }
});
</script>

<style scoped>
.query-version-selector {
  position: relative;
  width: 100%;
}

.suffix-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 4px;
}

:deep(.ant-select-selection-search-input) {
  height: 100%;
}

:deep(.ant-select-selector) {
  height: 38px !important;
  padding: 4px 11px !important;
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-select-selection-item) {
  line-height: 30px !important;
}
</style>