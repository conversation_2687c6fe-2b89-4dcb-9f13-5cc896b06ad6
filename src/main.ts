// @ts-ignore - 为了解决Vue中createApp导入问题
import {createApp} from 'vue'
import {createPinia} from 'pinia'

// 最优先导入dayjs配置（包含locale和plugins），必须在其他所有导入之前
import './plugins/dayjs'

// 导入 Stagewise Toolbar 插件
import setupStagewise from './plugins/stagewise-toolbar'

// 废弃旧的axios拦截器，由新的中间件替代
// import { setupAxiosInterceptor } from './plugins/axios-interceptor'
// setupAxiosInterceptor() // 明确初始化axios拦截器
// 删除旧的Mock服务导入
// import setupMock from './services/api'
import App from './App.vue'
import router from './router'

// 导入自定义样式，确保Tailwind可以正确应用
import './styles/index.css'
// import './styles/dropdown.css'  // 导入全局下拉菜单样式修复
import './styles/z-index.css' // 导入全局z-index管理样式
import './utils/ui/select-enhancer.css' // 导入select增强样式
import {initQueryTemplates} from './services/queryTemplates'
import './utils/ui/selectEnhancer' // 导入select交互增强脚本
// 引入Font Awesome
import '@fortawesome/fontawesome-free/css/all.min.css'

// 引入Ant Design Vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// 导入Ant Design Vue中文配置
import zhCN from './plugins/antd-locale'

// 引入ECharts配置
import './plugins/echarts'

// 引入消息服务插件
import {installMessageService} from './services/message'

// 导入修复文件 (会根据环境变量自动处理)
import './mockDataSources-fix';

// 导入SQL参数监控工具
import {initSqlParamMonitor} from '@/utils/sql-param-monitor';

// 预加载Monaco编辑器
import '@/plugins/monaco';

// 初始化查询模板
initQueryTemplates()

// 删除旧的Mock API服务初始化
// setupMock()

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(createPinia())

app.use(router)
// @ts-ignore 忽略类型检查错误
app.use(Antd, { locale: zhCN })
installMessageService.install(app)  // 正确调用消息服务插件
app.mount('#app')

// 初始化SQL参数监控
initSqlParamMonitor();

// 初始化 Stagewise Toolbar (仅在开发环境中)
setupStagewise();
