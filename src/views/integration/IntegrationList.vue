<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useIntegrationStore } from '@/stores/integration';
import { message } from '@/services/message';
import { storeToRefs } from 'pinia';
import type { Integration } from '@/types/integration';
import { convertToStandardConfig } from '@/utils/configConverter';
import { transformFrontendIntegrationToApi } from '@/utils/apiTransformer';
import Pagination from '@/components/common/Pagination.vue'; // 导入通用分页组件

// 路由相关
const router = useRouter();

// Store
const integrationStore = useIntegrationStore();
const { integrations, loading } = storeToRefs(integrationStore);

// 状态
const searchQuery = ref('');
const selectedType = ref('all');
const selectedStatus = ref('all');
const currentPage = ref(1);
const pageSize = ref(10);
const showDeleteConfirm = ref(false);
const integrationToDelete = ref<Integration | null>(null);

// 选项定义
const typeOptions = [
  { value: 'all', label: '所有类型' },
  { value: 'SIMPLE_TABLE', label: '简单表格' },
  { value: 'TABLE', label: '高级表格' },
  { value: 'CHART', label: '数据图表' }
];

const statusOptions = [
  { value: 'all', label: '所有状态' },
  { value: 'DRAFT', label: '草稿' },
  { value: 'ACTIVE', label: '已激活' },
  { value: 'INACTIVE', label: '已停用' }
];

// 计算属性

// 总页数，通过服务器响应确定
const totalPages = computed(() => {
  // 使用服务器返回的分页信息
  const result = integrationStore.lastQueryResult;
  if (result && result.pages) {
    return result.pages;
  }
  
  // 如果没有分页信息，则返回1
  return 1;
});

// 计算分页号码显示
const pageNumbers = computed(() => {
  const total = totalPages.value;
  const current = currentPage.value;
  const result = [];
  
  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      result.push(i);
    }
  } else {
    // 总是显示第一页
    result.push(1);
    
    // 当前页靠近开始
    if (current <= 4) {
      result.push(2, 3, 4, 5);
      result.push('...');
      result.push(total);
    } 
    // 当前页靠近结束
    else if (current >= total - 3) {
      result.push('...');
      result.push(total - 4, total - 3, total - 2, total - 1);
      result.push(total);
    } 
    // 当前页在中间
    else {
      result.push('...');
      result.push(current - 1, current, current + 1);
      result.push('...');
      result.push(total);
    }
  }
  
  return result;
});

// 生命周期钩子
onMounted(async () => {
  await fetchIntegrations();
});

// 获取集成列表
const fetchIntegrations = async () => {
  loading.value = true;
  
  try {
    // 构建查询参数
    const params: {
      page: number;
      size: number;
      name?: string;
      type?: 'SIMPLE_TABLE' | 'TABLE' | 'CHART';
      status?: 'DRAFT' | 'ACTIVE' | 'INACTIVE';
    } = {
      page: currentPage.value,
      size: pageSize.value
    };
    
    // 添加筛选条件
    if (searchQuery.value) {
      params.name = searchQuery.value.trim();
    }
    
    params.type = selectedType.value as 'SIMPLE_TABLE' | 'TABLE' | 'CHART';
    params.status = selectedStatus.value as 'DRAFT' | 'ACTIVE' | 'INACTIVE';
    
    // 调试日志
    console.log('发送查询参数:', params);
    
    const result = await integrationStore.fetchIntegrations(params);
    
    if (result && result.length > 0) {
      if (integrationStore.error) {
        console.warn('获取集成列表部分成功，有数据但存在错误:', integrationStore.error);
        message.warning({
          content: '获取集成列表部分成功',
          description: integrationStore.error,
          duration: 4000
        });
      } else {
        console.log('获取集成列表成功，获取到', result.length, '条数据');
      }
    } else {
      if (integrationStore.error) {
        console.error('获取集成列表失败:', integrationStore.error);
        message.error({
          content: '获取集成列表失败',
          description: integrationStore.error,
          duration: 5000
        });
      } else {
        console.log('获取集成列表成功，但没有数据');
        if (searchQuery.value || selectedType.value !== 'all' || selectedStatus.value !== 'all') {
          message.info({
          content: '未找到符合条件的集成',
          description: '没有符合当前筛选条件的集成数据，请尝试修改筛选条件',
          duration: 3000
        });
        } else {
          message.info({
          content: '暂无集成数据',
          description: '系统中尚未创建任何集成，请点击“创建集成”按钮创建第一个集成',
          duration: 3000
        });
        }
      }
    }
  } catch (error) {
    console.error('获取集成列表时发生异常', error);
    message.error({
      content: '获取集成列表失败',
      description: error instanceof Error ? error.message : '未知错误，请检查网络连接或稍后重试',
      duration: 5000
    });
  } finally {
    loading.value = false;
  }
};

// 创建新集成
const createIntegration = () => {
  try {
    if (router) {
      router.push('/integration/create');
    } else {
      // 备用方案：使用window.location进行导航
      window.location.href = '/integration/create';
      console.warn('使用window.location进行导航，因为router未定义');
    }
  } catch (error) {
    // 捕获任何可能的导航错误并使用备用方案
    console.error('router导航失败，尝试使用window.location:', error);
    window.location.href = '/integration/create';
  }
};

// 编辑集成
const editIntegration = (id: string) => {
  try {
    if (router) {
      router.push(`/integration/edit/${id}`);
    } else {
      // 备用方案：使用window.location进行导航
      window.location.href = `/integration/edit/${id}`;
      console.warn('使用window.location进行导航，因为router未定义');
    }
  } catch (error) {
    // 捕获任何可能的导航错误并使用备用方案
    console.error('router导航失败，尝试使用window.location:', error);
    window.location.href = `/integration/edit/${id}`;
  }
};

// 预览集成
const previewIntegration = (id: string, type?: string) => {
  try {
    // 检查是否有发布后的URL
    const previewUrl = `https://qaboss.yeepay.com/low-code-editor/2_18_3/preview/${id}`;
    
    // 尝试在新窗口打开低代码平台预览页面
    window.open(previewUrl, '_blank');
    
    // 添加提示
    message.info({
      content: '正在打开预览页面',
      description: '正在尝试打开低代码平台预览页面，如果页面不存在，请先发布集成',
      duration: 3000
    });
  } catch (error) {
    console.error('打开预览失败:', error);
    message.error({
      content: '打开预览页面失败',
      description: '无法打开预览页面，请确保您已经成功发布了集成到低代码平台',
      duration: 5000
    });
  }
};

// 下载转换后的JSON
const downloadStandardJSON = async (integration: Integration) => {
  try {
    console.log('[列表下载JSON] 开始处理，集成ID:', integration.id);
    
    // 首先获取完整的集成详情，确保有最新数据
    const integrationDetail = await integrationStore.fetchIntegrationById(integration.id);
    
    if (!integrationDetail) {
      throw new Error('获取集成详情失败');
    }
    
    // 记录详情数据的关键部分
    console.log('[列表下载JSON] 获取到集成详情关键字段:', {
      id: integrationDetail.id,
      name: integrationDetail.name,
      type: integrationDetail.type,
      queryParamsCount: integrationDetail.queryParams?.length || 0,
      hasTableConfig: !!integrationDetail.tableConfig,
      columnsCount: integrationDetail.tableConfig?.columns?.length || 0,
      hasChartConfig: !!integrationDetail.chartConfig
    });
    
    // 检查是否从后端获取到完整数据
    const missingQueryParams = !integrationDetail.queryParams || integrationDetail.queryParams.length === 0;
    const missingTableConfig = (integrationDetail.type === 'TABLE' || integrationDetail.type === 'SIMPLE_TABLE') && 
                              (!integrationDetail.tableConfig || !integrationDetail.tableConfig.columns);
    
    // 如果缺少关键数据，尝试从本地存储获取
    if (missingQueryParams || missingTableConfig) {
      console.warn('[列表下载JSON] 从API获取的数据不完整，尝试从本地存储补充');
      
      try {
        const storageKey = `integration_config_${integrationDetail.id}`;
        const storedConfigJson = localStorage.getItem(storageKey);
        
        if (storedConfigJson) {
          const storedConfig = JSON.parse(storedConfigJson);
          console.log('[列表下载JSON] 从本地存储获取的配置:', {
            queryParamsCount: storedConfig.queryParams?.length || 0,
            hasTableConfig: !!storedConfig.tableConfig,
            columnsCount: storedConfig.tableConfig?.columns?.length || 0
          });
          
          // 补充缺失的数据
          if (missingQueryParams && storedConfig.queryParams && storedConfig.queryParams.length > 0) {
            console.log('[列表下载JSON] 使用本地存储中的queryParams补充');
            integrationDetail.queryParams = storedConfig.queryParams;
          }
          
          if (missingTableConfig && storedConfig.tableConfig && 
             (integrationDetail.type === 'TABLE' || integrationDetail.type === 'SIMPLE_TABLE')) {
            console.log('[列表下载JSON] 使用本地存储中的tableConfig补充');
            integrationDetail.tableConfig = storedConfig.tableConfig;
          }
        } else {
          console.warn('[列表下载JSON] 本地存储中未找到配置数据');
        }
      } catch (storageError) {
        console.error('[列表下载JSON] 从本地存储补充配置失败:', storageError);
      }
    }
    
    // 首先使用API转换器转换集成数据
    const apiIntegration = transformFrontendIntegrationToApi(integrationDetail);
    
    // 确保有查询参数和值
    const queryParams = integrationDetail.queryParams || [];
    const paramValues: Record<string, any> = {};
    
    console.log('[列表下载JSON] 查询参数数量:', queryParams.length);
    
    // 为每个参数设置默认值
    queryParams.forEach((param: any) => {
      paramValues[param.name] = param.defaultValue || '';
    });
    
    // 准备转换数据对象
    const integrationData = {
      meta: {
        database: integrationDetail.dataSourceId || '',
        schema: '',
        table: '',
        pageCode: integrationDetail.id || 'integration'
      },
      type: apiIntegration.type || integrationDetail.type,
      queryParams: apiIntegration.queryParams || [],
      tableConfig: {
        ...(apiIntegration.tableConfig || {}),
        // 确保columns数组存在且完整
        columns: apiIntegration.tableConfig?.columns || []
      },
      chartConfig: apiIntegration.chartConfig || null
    };
    
    console.log('[列表下载JSON] 转换前数据检查:', {
      hasQueryParams: integrationData.queryParams?.length || 0,
      hasTableConfigColumns: integrationData.tableConfig.columns?.length || 0
    });
    
    // 转换为标准JSON格式
    const standardConfig = convertToStandardConfig(integrationData);
    
    console.log('[列表下载JSON] 转换后数据检查:', {
      hasFilter: standardConfig.filter?.length || 0,
      hasList: standardConfig.list?.length || 0
    });
    
    // 转换为美化的JSON字符串
    const jsonString = JSON.stringify(standardConfig, null, 2);
    
    // 创建Blob并下载
    const blob = new Blob([jsonString], { 
      type: 'application/json;charset=utf-8' 
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // 使用更友好的文件名
    const safeFileName = (integration.name || 'integration')
      .replace(/[^a-z0-9\s-_]/gi, '')  // 移除特殊字符
      .replace(/\s+/g, '_');            // 空格替换为下划线
    
    link.download = `${safeFileName}_standard_config_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    // 调试数据验证
    try {
      const testParse = JSON.parse(jsonString);
      console.log('[列表下载JSON] 验证下载数据可以正确解析:', true);
      message.success('JSON配置下载成功');
    } catch (parseError) {
      console.error('[列表下载JSON] 验证下载数据解析失败:', parseError);
      message.warning('JSON配置已下载，但格式可能有问题');
    }
  } catch (error) {
    console.error('[列表下载JSON] 下载JSON配置失败:', error);
    message.error(`下载JSON配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 删除集成确认
const confirmDelete = (integration: Integration) => {
  integrationToDelete.value = integration;
  showDeleteConfirm.value = true;
};

// 执行删除操作
const deleteIntegration = async () => {
  if (!integrationToDelete.value) return;
  
  try {
    await integrationStore.deleteIntegration(integrationToDelete.value.id);
    message.success('删除集成成功');
    showDeleteConfirm.value = false;
    integrationToDelete.value = null;
  } catch (error) {
    console.error('删除集成失败', error);
    message.error('删除集成失败');
  }
};

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false;
  integrationToDelete.value = null;
};

// 切换状态
const toggleStatus = async (integration: Integration) => {
  const newStatus = integration.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
  
  try {
    await integrationStore.updateIntegrationStatus(integration.id, newStatus);
    message.success(`集成已${newStatus === 'ACTIVE' ? '激活' : '停用'}`);
  } catch (error) {
    console.error('更新集成状态失败', error);
    message.error('更新集成状态失败');
  }
};

// 重置筛选条件
const clearFilters = () => {
  searchQuery.value = '';
  selectedType.value = 'all';
  selectedStatus.value = 'all';
  currentPage.value = 1;
};

// 执行查询
const executeQuery = () => {
  currentPage.value = 1; // 重置为第一页
  console.log('执行查询，筛选条件:', {
    搜索: searchQuery.value,
    类型: selectedType.value,
    状态: selectedStatus.value
  });
  fetchIntegrations();
};

// 页码变更处理
const handlePageChange = (page: number | string) => {
  // 处理省略号情况
  if (page === '...') return;
  
  currentPage.value = Number(page);
  console.log('[分页] 切换到页码:', currentPage.value);
  // 重新获取数据
  fetchIntegrations();
};

// 每页条数变更处理
const handlePageSizeChange = () => {
  currentPage.value = 1; // 重置为第一页
  fetchIntegrations();
};

// 获取状态标签样式
const getStatusClass = (status: string): string => {
  switch (status) {
    case 'ACTIVE':
      return 'bg-green-100 text-green-800';
    case 'INACTIVE':
      return 'bg-gray-100 text-gray-800';
    case 'DRAFT':
      return 'bg-yellow-100 text-yellow-800';
    case 'ERROR':
      return 'bg-red-100 text-red-800';
    case 'SYNCING':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// 获取状态名称
const getStatusName = (status: string): string => {
  switch (status) {
    case 'ACTIVE':
      return '已激活';
    case 'INACTIVE':
      return '已停用';
    case 'DRAFT':
      return '草稿';
    default:
      return status;
  }
};

// 获取类型名称
const getTypeName = (type: string): string => {
  switch (type) {
    case 'SIMPLE_TABLE':
      return '简单表格';
    case 'TABLE':
      return '高级表格';
    case 'CHART':
      return '数据图表';
    default:
      return type;
  }
};

// 格式化日期
const formatDate = (dateInput: string | Array<number> | null | undefined): string => {
  if (!dateInput) return '-';
  
  let date: Date;
  
  if (Array.isArray(dateInput)) {
    // 处理数组格式 [年, 月, 日, 时, 分, 秒]
    if (dateInput.length >= 6) {
      // JavaScript中月份是0-11，因此需要将月份-1
      date = new Date(dateInput[0], dateInput[1] - 1, dateInput[2], dateInput[3], dateInput[4], dateInput[5]);
    } else if (dateInput.length >= 3) {
      // 至少有年月日
      date = new Date(dateInput[0], dateInput[1] - 1, dateInput[2]);
    } else {
      return '-';
    }
  } else if (typeof dateInput === 'string') {
    // 处理ISO字符串格式
    date = new Date(dateInput);
  } else {
    return '-';
  }
  
  // 验证日期是否有效
  if (isNaN(date.getTime())) {
    return '-';
  }
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};
</script>

<template>
  <div class="container mx-auto px-4 py-6">
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">系统集成</h1>
        <button 
          @click="createIntegration"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <i class="fas fa-plus mr-2"></i>
          创建集成
        </button>
      </div>
    </div>
    
    <!-- 过滤器 -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索 -->
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
            搜索
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input 
              id="search"
              v-model="searchQuery"
              type="text"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="搜索集成名称或描述"
              @keyup.enter="executeQuery"
            />
          </div>
        </div>
        
        <!-- 类型过滤 -->
        <div>
          <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-1">
            集成类型
          </label>
          <div class="relative select-container">
            <select
              id="type-filter"
              v-model="selectedType"
              class="block w-full py-2 pl-3 pr-10 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
            >
              <option v-for="option in typeOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>
        </div>
        
        <!-- 状态过滤 -->
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">
            状态
          </label>
          <div class="relative select-container">
            <select
              id="status-filter"
              v-model="selectedStatus"
              class="block w-full py-2 pl-3 pr-10 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
            >
              <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex items-end space-x-2">
          <button 
            @click="clearFilters"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-sync-alt mr-2"></i>
            重置筛选
          </button>
          <button 
            @click="executeQuery"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-search mr-2"></i>
            查询
          </button>
        </div>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <div class="bg-white shadow rounded-lg">
      <!-- 加载中 -->
      <div v-if="loading" class="p-10 text-center">
        <i class="fas fa-circle-notch fa-spin text-indigo-500 text-3xl mb-4"></i>
        <p class="text-gray-500">正在加载集成列表...</p>
      </div>
      
      <!-- 无数据时 -->
      <div v-else-if="integrations.length === 0" class="p-10 text-center">
        <div class="rounded-full bg-gray-100 h-16 w-16 flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-cubes text-gray-400 text-2xl"></i>
        </div>
        <h3 class="text-sm font-medium text-gray-900">暂无集成数据</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ searchQuery || selectedType !== 'all' || selectedStatus !== 'all' ? '没有符合筛选条件的集成' : '暂无数据' }}
        </p>
        <div class="mt-6">
          <button 
            @click="createIntegration"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-plus mr-2"></i>
            创建集成
          </button>
        </div>
      </div>
      
      <!-- 数据表格 -->
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                更新时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作人
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="integration in integrations" :key="integration.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-indigo-100 text-indigo-600">
                    <i class="fas" :class="integration.type === 'CHART' ? 'fa-chart-bar' : 'fa-table'"></i>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-indigo-600 hover:text-indigo-800 cursor-pointer" @click="editIntegration(integration.id)">
                      {{ integration.name }}
                    </div>
                    <div v-if="integration.description" class="text-sm text-gray-500 max-w-md truncate">
                      {{ integration.description }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                  {{ getTypeName(integration.type) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="getStatusClass(integration.status)"
                >
                  {{ getStatusName(integration.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(integration.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(integration.updatedAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ integration.updatedBy || '系统' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                  @click="editIntegration(integration.id)"
                  class="text-indigo-600 hover:text-indigo-900 mx-1"
                  title="编辑"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button 
                  @click="toggleStatus(integration)"
                  :class="[
                    'mx-1',
                    integration.status === 'ACTIVE' ? 'text-gray-600 hover:text-gray-900' : 'text-green-600 hover:text-green-900'
                  ]"
                  :title="integration.status === 'ACTIVE' ? '停用' : '激活'"
                >
                  <i :class="[
                    integration.status === 'ACTIVE' ? 'fas fa-pause-circle' : 'fas fa-play-circle'
                  ]"></i>
                </button>
                <button 
                  @click="confirmDelete(integration)"
                  class="text-red-600 hover:text-red-900 mx-1"
                  title="删除"
                >
                  <i class="fas fa-trash-alt"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 底部分页 -->
      <Pagination
        v-if="totalPages > 0"
        :current-page="currentPage"
        :total-items="integrationStore.lastQueryResult?.total || 0"
        :page-size="pageSize"
        @page-change="handlePageChange"
      />
    </div>
    
    <!-- 删除确认对话框 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  删除集成
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    确定要删除"{{ integrationToDelete?.name }}"吗？此操作无法撤销。
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button 
              @click="deleteIntegration"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              删除
            </button>
            <button 
              @click="cancelDelete"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>