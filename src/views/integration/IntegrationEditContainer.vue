<template>
  <div class="container mx-auto px-4 py-4 min-h-screen">
    <!-- 顶部操作栏 -->
    <ActionBar 
      :is-create-mode="isCreateMode"
      :integration-id="integration.id"
      :loading="loading"
      :save-loading="saveLoading"
      :publish-loading="isPublishing"
      :show-export-button="!isCreateMode"
      :show-preview-button="!isCreateMode && hasPublishedToLowCode"
      @preview="previewIntegration"
      @export-json="exportJson"
      @cancel="cancelEdit"
      @save="saveIntegration"
      @publish-to-lowcode="handlePublishRequest"
    />

    <!-- 表单内容 -->
    <div class="space-y-6 mb-6">
      <!-- 基本信息 -->
      <BasicInfoForm
        :integration="integration"
        :validation-errors="validationErrors"
        :show-validation-errors="showValidationErrors"
        :versions="versions"
        :is-loading-versions="isLoadingVersions"
        @type-selected="selectIntegrationType"
        @data-source-selected="handleDataSourceSelected"
        @query-changed="handleQueryChange"
      />
      
      <!-- 版本管理组件 -->
      <VersionManager
        ref="versionManagerRef"
        :query-id="integration.queryId"
        :version-id="integration.versionId"
        @version-changed="updateIntegrationVersion"
        @versions-loaded="handleVersionsLoaded"
        @loading-start="isLoadingVersions = true"
        @loading-end="isLoadingVersions = false"
      />
      
      <!-- 数据条件配置组件 -->
      <QueryConditionsPanel
        v-if="showConfigSections"
        v-model="queryParams"
        :param-values="paramValues"
        :validation-errors="validationErrors"
        :query-id="integration.queryId"
        :data-source-id="integration.dataSourceId"
        :integration-type="integration.type"
        @load-params-from-query="handleDataSourceParamsLoad"
        @validate="updateValidationErrors"
        ref="queryConditionsPanel"
      />
      
      <!-- 配置区域布局优化 - 将表格配置和图表配置并排显示(如果同时存在) -->
      <div class="grid grid-cols-1 gap-6">
        <!-- 表格配置 -->
        <div 
          v-if="showConfigSections && (integration.type === 'TABLE' || integration.type === 'SIMPLE_TABLE')" 
          class="bg-white shadow rounded-lg overflow-hidden"
        >
          <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-900 flex justify-between items-center">
            <span>表格配置 <span class="text-xs text-gray-500 ml-1">(仅在表格类型下显示)</span></span>
            <button 
              v-if="tableConfigRef"
              @click="tableConfigRef.importFieldsFromData && tableConfigRef.importFieldsFromData()" 
              class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
            >
              <i class="fas fa-file-import mr-2"></i>从数据配置导入字段
            </button>
          </div>
          <div class="p-4">
            <!-- 表格完整配置 -->
            <TableConfigTable
              v-model="tableConfig"
              :queryId="integration.queryId"
              :query-version-ref="versionManagerRef"
              ref="tableConfigRef"
            />
          </div>
        </div>
        
        <!-- 图表配置 -->
        <div v-if="showConfigSections && integration.type === 'CHART'" class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-900 flex justify-between items-center">
            <span>图表配置 <span class="text-xs text-gray-500 ml-1">(仅在图表类型下显示)</span></span>
            <button 
              v-if="chartConfigRef"
              @click="chartConfigRef.importFieldsFromData && chartConfigRef.importFieldsFromData()" 
              class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
            >
              <i class="fas fa-file-import mr-2"></i>从数据配置导入字段
            </button>
          </div>
          <div class="p-4">
            <!-- 使用ChartConfigTable组件 -->
            <ChartConfigTable
              v-model="chartConfig"
              :query-id="integration.queryId"
              :query-params="paramValues"
              :available-fields="availableFields"
              :version-id="integration.versionId"
              @refresh-preview="refreshChartPreview"
              @fields-loaded="handleFieldsLoaded"
              ref="chartConfigRef"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 发布到低代码状态弹窗 -->
  <PublishManager
    ref="publishManagerRef"
    :integration="integration"
    :query-params="queryParams"
    :table-config="tableConfig"
    :chart-config="chartConfig"
    :validation-errors="validationErrors"
    @validate-form="runValidation"
    @save-integration="saveIntegration"
    @publish-started="isPublishing = true"
    @publish-completed="handlePublishCompleted"
  />
  
  <!-- 导出JSON组件 -->
  <ExportManager
    ref="exportManagerRef"
    :integration="integration"
    :query-params="queryParams"
    :table-config="tableConfig"
    :chart-config="chartConfig"
  />

  <!-- 验证管理组件 -->
  <ValidationManager
    ref="validationManagerRef"
    :integration="integration"
    :query-params="queryParams"
    :query-conditions-panel-ref="queryConditionsPanel"
    :validation-errors="validationErrors"
    @validation-status-changed="handleValidationStatusChanged"
    @show-validation-errors="showValidationErrors = $event"
    @update-validation-errors="updateValidationErrors"
  />
  
  <!-- 数据源参数管理组件 -->
  <DataSourceParamManager
    ref="dataSourceParamManagerRef"
    :integration="integration"
    :query-conditions-panel-ref="queryConditionsPanel"
    @params-loaded="handleParamsLoaded"
    @fields-loaded="handleFieldsLoaded"
    @param-values-updated="handleParamValuesUpdated"
  />
  
  <!-- 保存管理组件 -->
  <SaveManager
    ref="saveManagerRef"
    :integration="integration"
    :query-params="queryParams"
    :validation-manager-ref="validationManagerRef"
    :table-config="tableConfig"
    :chart-config="chartConfig"
    @save-started="saveLoading = true"
    @save-completed="handleSaveCompleted"
    @id-updated="handleIdUpdated"
  />
  
  <!-- 本地存储恢复组件 -->
  <StorageRestoreManager
    ref="storageRestoreManagerRef"
    :integration="integration"
    :query-params="queryParams"
    :param-values="paramValues"
    :is-create-mode="isCreateMode"
    @params-loaded="handleParamsLoaded"
    @storage-restored="handleStorageRestored"
    @restore-error="handleRestoreError"
  />
</template>

<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useIntegrationStore } from '@/stores/integration';
import { useQueryStore } from '@/stores/query';
import { storeToRefs } from 'pinia';
import { resetTableConfig, resetChartConfig } from '@/components/integration/container/utils/configReset';
import type { IntegrationType, IntegrationStatus, TableConfig, ChartConfig, QueryParam, IntegrationData } from '@/types/unified-integration';
import { notification } from 'ant-design-vue';
import { message } from '@/services/message';

// 导入子组件
import ActionBar from '@/components/integration/container/ActionBar.vue';
import BasicInfoForm from '@/components/integration/container/BasicInfoForm.vue';
import TableConfigTable from '@/components/integration/tablemode/TableConfigTableHybrid.vue';
import ChartConfigTable from '@/components/integration/chartmode/ChartConfigTable.vue';
import QueryConditionsPanel from '@/components/integration/queryparams/QueryConditionsPanel.vue';
import PublishManager from '@/components/integration/publish/PublishManager.vue';
import ExportManager from '@/components/integration/export/ExportManager.vue';
import VersionManager from '@/components/integration/version/VersionManager.vue';
import ValidationManager from '@/components/integration/validation/ValidationManager.vue';
import DataSourceParamManager from '@/components/integration/datasource/DataSourceParamManager.vue';
import SaveManager from '@/components/integration/save/SaveManager.vue';
import StorageRestoreManager from '@/components/integration/storage/StorageRestoreManager.vue';

// 路由相关
const route = useRoute();
const router = useRouter();
const integrationStore = useIntegrationStore();
const queryStore = useQueryStore();

// 表单状态
const isCreateMode = computed(() => !route.params.id);
const showValidationErrors = ref(false);
const validationErrors = reactive<Record<string, string>>({});

// 和Store通信
const { loading } = storeToRefs(integrationStore);
const { queries } = storeToRefs(queryStore);

// 集成对象
const integration = reactive<IntegrationData>({
  id: '',
  name: '',
  description: '',
  type: 'TABLE' as IntegrationType,
  status: 'DRAFT',
  dataSourceId: '',
  queryId: '',
  versionId: '',
  parameters: [],
  tableConfig: resetTableConfig(),
  chartConfig: resetChartConfig(),
  createTime: '',
  updateTime: '',
  meta: {
    database: '',
    schema: '',
    table: '',
    pageCode: '',
    apis: {
      // 标准化的API接口定义
      query: {
        method: 'GET',
        path: '/api/queries/{queryId}/versions/{versionId}/execute'
      },
      download: {
        method: 'GET',
        path: '/download'
      }
    }
  }
});

// 版本相关状态
const versions = ref<Array<{id: string, versionNumber: number, isLatest: boolean}>>([]);
const isLoadingVersions = ref(false);

// 查询参数和值
const queryParams = ref<QueryParam[]>([]);
const paramValues = reactive<Record<string, any>>({});
const availableFields = ref<string[]>([]);

// 配置对象
const tableConfig = computed({
  get: () => integration.tableConfig as any,
  set: (value: any) => {
    integration.tableConfig = value;
  }
});

const chartConfig = computed({
  get: () => integration.chartConfig as any,
  set: (value: any) => {
    integration.chartConfig = value;
  }
});

// 是否显示配置区域
const showConfigSections = computed(() => !!integration.queryId);

// 判断是否已发布到低代码平台
const hasPublishedToLowCode = computed(() => {
  return !!integrationStore.lastPublishedPreviewUrl && integration.id && integration.status === 'ACTIVE';
});

// 组件实例引用
const queryConditionsPanel = ref(null);

// 添加TableConfigTable组件的类型定义
interface TableConfigTableInstance {
  importFieldsFromData: () => void;
  [key: string]: any;
}

// 使用带类型的组件引用
const tableConfigRef = ref<TableConfigTableInstance | null>(null);

// 添加ChartConfigTable组件的类型定义
interface ChartConfigTableInstance {
  importFieldsFromData: () => void;
  [key: string]: any;
}

// 使用带类型的组件引用
const chartConfigRef = ref<ChartConfigTableInstance | null>(null);

// 添加保存和发布状态
const saveLoading = ref(false);
const isPublishing = ref(false);

// 发布到低代码弹窗状态
const publishManagerRef = ref<{
  publishToLowCode: () => Promise<void>;
  closePublishModal: () => void;
} | null>(null);

// 导出JSON组件引用
const exportManagerRef = ref<{
  exportJson: () => void;
} | null>(null);

// 版本管理组件引用
const versionManagerRef = ref<{
  handleVersionChange: (versionInfo: any) => void;
  loadQueryVersions: (queryId: string) => Promise<void>;
} | null>(null);

// 验证管理组件引用
const validationManagerRef = ref<{
  validateFormWithFeedback: () => Promise<boolean>;
  clearValidationErrors: () => void;
  updateValidationErrors: (errors: Record<string, string>) => void;
  hideValidationErrors: () => void;
  showErrors: () => void;
} | null>(null);

// 数据源参数管理组件引用
const dataSourceParamManagerRef = ref<any>(null);

// 保存管理组件引用
const saveManagerRef = ref<{
  saveIntegration: () => Promise<void>;
  getIntegrationObj: () => any;
  saveLoading: Ref<boolean>;
} | null>(null);

// 本地存储恢复组件引用
const storageRestoreManagerRef = ref<{
  restoreFromLocalStorage: (integrationId: string) => Promise<boolean>;
  checkAndRestoreConfig: (data: any) => Promise<void>;
  initializeDefaultConfigs: () => void;
} | null>(null);

// 方法：处理数据源选择
const handleDataSourceSelected = (id: string) => {
  integration.dataSourceId = id;
  
  // 如果当前查询不属于新选择的数据源，清空查询选择
  if (integration.queryId) {
    const currentQuery = queries.value.find(q => q.id === integration.queryId);
    if (currentQuery && currentQuery.dataSourceId !== id) {
      integration.queryId = '';
      // 同时清空版本
      integration.versionId = '';
      versions.value = [];
    }
  }
};

// 方法：选择集成类型
const selectIntegrationType = (type: IntegrationType) => {
  integration.type = type;
  // 根据类型重置相应的配置
  if (type === 'TABLE' || type === 'SIMPLE_TABLE') {
    integration.tableConfig = resetTableConfig();
    integration.chartConfig = undefined;
  } else if (type === 'CHART') {
    integration.chartConfig = resetChartConfig();
    integration.tableConfig = undefined;
  }
};

// 方法：处理查询变更
const handleQueryChange = async (queryId: string) => {
  console.log('处理查询变更，queryId:', queryId);
  
  integration.queryId = queryId; // 确保设置查询ID
  
  if (!queryId) {
    queryParams.value = [];
    return;
  }
  
  // 清空版本
  integration.versionId = '';
  versions.value = [];
  
  // 版本管理由VersionManager组件处理
  // loadQueryVersions将由组件的watch自动触发
  
  // 重置验证状态
  showValidationErrors.value = false;
  // 注释掉自动参数加载逻辑，防止切换查询时自动加载parameters
  // if (dataSourceParamManagerRef.value && dataSourceParamManagerRef.value.loadParamsFromQuery) {
  //   await dataSourceParamManagerRef.value.loadParamsFromQuery();
  // }
};

/**
 * 处理版本更新事件
 */
const updateIntegrationVersion = (versionInfo: any) => {
  integration.versionId = versionInfo;
};

/**
 * 处理版本列表加载完成事件
 */
const handleVersionsLoaded = (loadedVersions: Array<{id: string, versionNumber: number, isLatest: boolean}>) => {
  versions.value = loadedVersions;
};

// 处理数据源参数加载
const handleDataSourceParamsLoad = async () => {
  console.log('[IntegrationEditContainer] 处理数据源参数加载');
  
  // 检查是否有dataSourceParamManagerRef
  if (!dataSourceParamManagerRef.value) {
    console.warn('[IntegrationEditContainer] 未找到数据源参数管理器引用');
    return;
  }

  try {
    // 检查是否有查询ID
    if (!integration.queryId) {
      console.warn('[IntegrationEditContainer] 查询ID为空，无法加载参数');
      message.warning('请先选择查询');
      return;
    }
    
    // 使用dataSourceParamManager加载参数（改为loadFieldsAsQueryParams，确保用searchFields）
    if (dataSourceParamManagerRef.value.loadFieldsAsQueryParams) {
      console.log('[IntegrationEditContainer] 调用loadFieldsAsQueryParams方法从API加载参数');
      const params = await (dataSourceParamManagerRef.value as { loadFieldsAsQueryParams?: () => Promise<any[]> }).loadFieldsAsQueryParams?.();
      
      // 打印详细参数信息以便调试
      console.log('[IntegrationEditContainer] loadFieldsAsQueryParams返回结果类型:', typeof params);
      console.log('[IntegrationEditContainer] loadFieldsAsQueryParams返回的参数数量:', params ? params.length : 0);
      
      // 更新查询参数
      if (params && Array.isArray(params)) {
        console.log('[IntegrationEditContainer] 已加载参数:', params);
        queryParams.value = [...(params as any[])]; // 使用解构赋值确保创建新的引用
        
        // 更新UI显示
        setTimeout(() => {
          const panel = (queryConditionsPanel.value ?? undefined) as { refreshParamsList?: () => void } | undefined;
          if (panel && typeof panel.refreshParamsList === 'function') {
            panel.refreshParamsList();
          }
        }, 100);
        
        if (params.length > 0) {
          message.success(`成功从数据源加载${params.length}个参数`);
        } else {
          // 参数为空数组时的处理
          message.info('当前查询没有可用参数，已加载默认配置');
        }
      } else {
        console.warn('[IntegrationEditContainer] 参数格式无效');
        
        // 即使参数无效也尝试清空当前参数列表
        if (queryParams.value.length > 0) {
          queryParams.value = [];
          message.info('已清空现有参数');
        } else {
          message.info('未找到可用参数');
        }
      }
    } else {
      console.warn('[IntegrationEditContainer] dataSourceParamManager没有loadFieldsAsQueryParams方法');
      message.error('参数管理器不支持加载参数功能');
    }
  } catch (error) {
    console.error('[IntegrationEditContainer] 加载参数失败:', error);
    message.error(`加载参数失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 更新验证错误
const updateValidationErrors = (errors: Record<string, string>) => {
  Object.keys(validationErrors).forEach(key => delete validationErrors[key]);
  Object.assign(validationErrors, errors);
};

// 刷新图表预览
const refreshChartPreview = () => {
  // 可以实现图表预览刷新逻辑
};

// 取消编辑
const cancelEdit = () => {
  router.back();
};

// 预览集成
const previewIntegration = async () => {
  if (!validationManagerRef.value || !await validationManagerRef.value.validateFormWithFeedback()) return;
  
  try {
    // 检查是否有发布后的预览URL可用
    if (integrationStore.lastPublishedPreviewUrl) {
      // 使用window.open在新标签页打开低代码平台预览链接
      window.open(integrationStore.lastPublishedPreviewUrl, '_blank');
    } else {
      // 如果没有发布过或没有预览链接，提示用户
      message.warning('请先发布集成到低代码平台，才能预览');
    }
  } catch (error) {
    console.error('预览操作失败:', error);
    message.error('预览操作失败，请重试');
  }
};

/**
 * 导出JSON配置
 */
const exportJson = () => {
  if (!exportManagerRef.value) return;
  exportManagerRef.value.exportJson();
};

// 保存集成
const saveIntegration = async () => {
  if (!saveManagerRef.value) return;
  await saveManagerRef.value.saveIntegration();
};

/**
 * 发布集成到低代码平台
 */
const handlePublishRequest = async () => {
  console.log('[IntegrationEditContainer] 处理发布请求');
  
  if (!publishManagerRef.value) {
    console.error('[IntegrationEditContainer] 发布管理器引用不存在');
    message.error('发布组件未初始化，请刷新页面重试');
    return;
  }
  
  try {
    // 先检查是否已有发布进行中
    if (isPublishing.value) {
      console.warn('[IntegrationEditContainer] 已有发布任务进行中，请等待完成');
      message.warning('已有发布任务进行中，请等待完成');
      return;
    }
    
    // 调用PublishManager组件的发布方法
    console.log('[IntegrationEditContainer] 调用低代码发布流程，发布管理器状态:', publishManagerRef.value);
    await publishManagerRef.value.publishToLowCode();
  } catch (error) {
    console.error('[IntegrationEditContainer] 发布请求执行出错:', error);
    message.error('发布过程中出现错误: ' + (error instanceof Error ? error.message : '未知错误'));
    isPublishing.value = false;
  }
};

// 生命周期：页面加载时
onMounted(async () => {
  console.log('[IntegrationEditContainer] 页面加载完成，准备初始化组件');
  console.log('[IntegrationEditContainer] 创建模式:', isCreateMode.value);
  console.log('[IntegrationEditContainer] publishManagerRef初始状态:', publishManagerRef.value);
  console.log('[IntegrationEditContainer] 路由查询参数:', route.query);
  
  // 检查URL中是否有自动发布参数，并移除自动触发发布功能
  if (route.query.publish === 'true') {
    console.warn('[IntegrationEditContainer] 检测到自动发布参数，已禁用');
  }
  
  // 如果是编辑模式，加载现有集成
  if (!isCreateMode.value) {
    try {
      const id = route.params.id as string;
      loading.value = true;
      
      console.log('[IntegrationEditContainer] 开始加载集成数据，ID:', id);
      const data = await integrationStore.fetchIntegrationById(id);
      
      console.log('[IntegrationEditContainer] 加载到集成数据:', data);
      
      if (data) {
        // 更新集成数据 - 基本信息
        integration.id = data.id || '';
        integration.name = data.name || '';
        integration.description = data.description || '';
        integration.type = (data.type || 'TABLE') as IntegrationType;
        integration.status = (data.status || 'DRAFT') as IntegrationStatus;
        integration.dataSourceId = data.dataSourceId || '';
        integration.queryId = data.queryId || '';
        integration.versionId = data.versionId || '';
        integration.createTime = (data as any).createTime || '';
        integration.updateTime = (data as any).updateTime || '';
        integration.meta = (data as any).meta || {
          database: data.dataSourceId || '',
          schema: '',
          table: '',
          pageCode: data.id || '',
          apis: {}
        };
        
        // 使用StorageRestoreManager组件处理配置恢复
        if (storageRestoreManagerRef.value) {
          await storageRestoreManagerRef.value.checkAndRestoreConfig(data);
        }
        
        // 加载可用字段
        if (integration.queryId && (integration.type === 'CHART' || integration.type === 'TABLE')) {
          try {
            const fields = await queryStore.getQueryFields(integration.queryId);
            availableFields.value = fields || [];
          } catch (error) {
            console.error('获取字段失败:', error);
          }
        }
        // 注释掉自动参数加载逻辑，防止页面初始化时自动加载parameters
        // if (dataSourceParamManagerRef.value && dataSourceParamManagerRef.value.loadParamsFromQuery) {
        //   await dataSourceParamManagerRef.value.loadParamsFromQuery();
        // }
      } else {
        message.error('找不到该集成');
        router.push({ name: 'IntegrationList' });
      }
    } catch (error) {
      console.error('加载集成失败:', error);
      message.error('加载集成数据失败');
      router.push({ name: 'IntegrationList' });
    } finally {
      loading.value = false;
    }
  }
  
  // 页面加载完成后记录状态
  console.log('[IntegrationEditContainer] 页面加载完成，发布状态检查:', {
    isPublishing: isPublishing.value,
    publishManagerRef: publishManagerRef.value ? '已初始化' : '未初始化'
  });
});

// 处理发布完成事件
const handlePublishCompleted = (result: { success: boolean, id?: string, message?: string }) => {
  isPublishing.value = false;
  if (result.success && result.id) {
    message.success('发布到低代码平台成功');
  } else {
    message.error(result.message || '发布失败');
  }
};

// 处理验证状态变化
const handleValidationStatusChanged = (status: boolean) => {
  // 根据验证状态执行相应的逻辑
  console.log('验证状态变化:', status);
};

// 调用验证方法包装函数
const runValidation = async () => {
  console.log('[IntegrationEditContainer] 开始执行表单验证');
  if (!validationManagerRef.value) {
    console.error('[IntegrationEditContainer] 验证管理器引用不存在');
    return false;
  }
  try {
    const result = await validationManagerRef.value.validateFormWithFeedback();
    console.log('[IntegrationEditContainer] 表单验证结果:', result);
    
    // 如果验证通过，确保清空validationErrors对象
    if (result) {
      // 清空验证错误对象
      Object.keys(validationErrors).forEach(key => delete validationErrors[key]);
      console.log('[IntegrationEditContainer] 验证通过，清空了错误对象:', validationErrors);
    }
    
    return result;
  } catch (error) {
    console.error('[IntegrationEditContainer] 表单验证执行出错:', error);
    return false;
  }
};

// 处理参数加载完成事件
const handleParamsLoaded = (params: QueryParam[]) => {
  queryParams.value = params;
};

// 处理字段加载完成事件
const handleFieldsLoaded = (fields: any[]) => {
  console.log('[IntegrationEditContainer] 接收到字段列表:', fields);
  
  // 处理不同格式的字段数据
  if (Array.isArray(fields)) {
    // 如果字段已经是字符串数组，直接使用
    if (typeof fields[0] === 'string') {
      availableFields.value = fields;
    } 
    // 如果字段是对象数组，提取name属性
    else if (typeof fields[0] === 'object') {
      availableFields.value = fields.map(f => f.name || f.field || f.column_name || f);
    }
    
    console.log('[IntegrationEditContainer] 更新后的可用字段列表:', availableFields.value);
  } else {
    console.warn('[IntegrationEditContainer] 接收到无效的字段列表格式:', fields);
  }
};

// 处理参数值更新事件
const handleParamValuesUpdated = (values: Record<string, any>) => {
  // 更新paramValues对象中的所有键值
  Object.keys(values).forEach(key => {
    paramValues[key] = values[key];
  });
};

// 处理保存完成事件
const handleSaveCompleted = (success: boolean, id?: string) => {
  if (success && id) {
    console.log('保存成功，集成ID:', id);
    // 不显示任何成功消息，完全依赖SaveManager处理成功提示
    saveLoading.value = false;
  } else {
    console.log('保存失败');
    saveLoading.value = false;
  }
};

// 处理ID更新事件
const handleIdUpdated = (id: string) => {
  integration.id = id;
};

// 处理本地存储恢复事件
const handleStorageRestored = () => {
  console.log('本地存储恢复成功');
  // 保留这个成功消息，因为这是单独的功能
  message.success('配置已从本地存储恢复');
};

// 处理本地存储恢复错误事件
const handleRestoreError = (error: Error) => {
  console.error('本地存储恢复错误:', error);
  message.error('本地存储恢复失败');
};
</script>

<style scoped>
.force-border {
  border: 1px solid #d1d5db !important;
}

/* 错误状态的输入框边框样式 */
.error-border {
  border: 1px solid #ef4444 !important;
  box-shadow: 0 0 0 1px #ef4444 !important;
}
</style>