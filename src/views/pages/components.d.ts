// 声明Vue模块
declare module 'vue' {
  export function ref<T>(value: T): { value: T }
  export function computed<T>(getter: () => T): { value: T }
  export function onMounted(callback: () => void): void
  export function onUnmounted(callback: () => void): void
  export function nextTick(callback: () => void): Promise<void>
  export function watch<T>(source: () => T, callback: (value: T, oldValue: T) => void, options?: { immediate?: boolean }): void
  export function watch<T>(source: T[], callback: (values: T[], oldValues: T[]) => void, options?: { immediate?: boolean }): void
  export function reactive<T extends object>(target: T): T
}