{"openapi": "3.0.1", "info": {"title": "DataScope API Documentation", "description": "API documentation for DataScope application", "version": "1.0"}, "servers": [{"url": "http://localhost:8080/data-scope", "description": "Generated server url"}], "paths": {"/api/queries/{id}": {"get": {"tags": ["query-controller"], "operationId": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryDTO"}}}}}}, "put": {"tags": ["query-controller"], "operationId": "updateQuery", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveQueryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryDTO"}}}}}}, "delete": {"tags": ["query-controller"], "operationId": "deleteQuery", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseString"}}}}}}}, "/api/queries/versions/{versionId}": {"get": {"tags": ["query-controller"], "operationId": "getQueryVersion", "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}, "put": {"tags": ["query-controller"], "operationId": "updateQueryVersion", "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}}, "/api/integrations/{id}": {"get": {"tags": ["integration-controller"], "operationId": "getIntegrationById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationDTO"}}}}}}, "put": {"tags": ["integration-controller"], "operationId": "updateIntegration", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationDTO"}}}}}}, "delete": {"tags": ["integration-controller"], "operationId": "deleteIntegration", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseVoid"}}}}}}}, "/api/datasources/{id}": {"get": {"tags": ["datasource-controller"], "operationId": "getDatasource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceDTO"}}}}}}, "put": {"tags": ["datasource-controller"], "operationId": "updateDatasource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDataSourceRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceDTO"}}}}}}, "delete": {"tags": ["datasource-controller"], "operationId": "deleteDatasource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBoolean"}}}}}}}, "/api/queries": {"get": {"tags": ["query-controller"], "operationId": "getQueries", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "queryType", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "serviceStatus", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "dataSourceId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDir", "in": "query", "required": false, "schema": {"type": "string", "default": "desc"}}, {"name": "includeDrafts", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMapStringObject"}}}}}}, "post": {"tags": ["query-controller"], "operationId": "createQuery", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveQueryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryDTO"}}}}}}}, "/api/queries/{queryId}/versions/{versionId}/execute": {"post": {"tags": ["query-controller"], "operationId": "executeQueryVersion", "parameters": [{"name": "queryId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteQueryParams"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryResultDTO"}}}}}}}, "/api/queries/{queryId}/versions/{versionId}/activate": {"post": {"tags": ["query-controller"], "operationId": "activateQueryVersion", "parameters": [{"name": "queryId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBoolean"}}}}}}}, "/api/queries/{id}/versions": {"get": {"tags": ["query-controller"], "operationId": "getQueryVersions", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMapStringObject"}}}}}}, "post": {"tags": ["query-controller"], "operationId": "createQueryVersion", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}}, "/api/queries/{id}/favorite": {"post": {"tags": ["query-controller"], "operationId": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseString"}}}}}}, "delete": {"tags": ["query-controller"], "operationId": "unfavorite<PERSON><PERSON>y", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseString"}}}}}}}, "/api/queries/{id}/execute": {"post": {"tags": ["query-controller"], "operationId": "execute<PERSON>uery", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteQueryParams"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryResultDTO"}}}}}}}, "/api/queries/versions/{versionId}/publish": {"post": {"tags": ["query-controller"], "operationId": "publishQueryVersion", "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}}, "/api/queries/versions/{versionId}/deprecate": {"post": {"tags": ["query-controller"], "operationId": "deprecateQueryVersion", "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}}, "/api/queries/nl-to-sql": {"post": {"tags": ["query-controller"], "operationId": "nlToSql", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NaturalLanguageQueryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseNaturalLanguageToSqlResult"}}}}}}}, "/api/metadata/datasources/{dataSourceId}/sync": {"post": {"tags": ["metadata-controller"], "operationId": "syncMetadata", "parameters": [{"name": "dataSourceId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncMetadataRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseSyncMetadataResponse"}}}}}}}, "/api/integrations": {"get": {"tags": ["integration-controller"], "operationId": "getIntegrationList", "parameters": [{"name": "param", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/IntegrationQueryParam"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponsePageResponseIntegrationDTO"}}}}}}, "post": {"tags": ["integration-controller"], "operationId": "createIntegration", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationDTO"}}}}}}}, "/api/integrations/execute-query": {"post": {"tags": ["integration-controller"], "operationId": "executeIntegrationQuery", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteIntegrationQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationQueryResultDTO"}}}}}}}, "/api/integrations/api/integration/execute-query": {"post": {"tags": ["integration-controller"], "operationId": "executeIntegrationQueryCompat", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteIntegrationQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationQueryResultDTO"}}}}}}}, "/api/integration/execute-query": {"post": {"tags": ["integration-compat-controller"], "operationId": "executeIntegrationQuery_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteIntegrationQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationQueryResultDTO"}}}}}, "deprecated": true}}, "/api/datasources": {"get": {"tags": ["datasource-controller"], "operationId": "listDatasources", "parameters": [{"name": "param", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/DataSourceQueryParam"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponsePageResponseDataSourceDTO"}}}}}}, "post": {"tags": ["datasource-controller"], "operationId": "createDatasource", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDataSourceRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceDTO"}}}}}}}, "/api/datasources/{id}/test-connection": {"post": {"tags": ["datasource-controller"], "operationId": "testConnection", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseTestConnectionResultDTO"}}}}}}}, "/api/datasources/{id}/sync": {"post": {"tags": ["datasource-controller"], "operationId": "syncDatasourceMetadata", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncDataSourceRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseSyncStatusDTO"}}}}}}}, "/api/datasources/test-connection": {"post": {"tags": ["datasource-controller"], "operationId": "testNewConnection", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestConnectionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseTestConnectionResultDTO"}}}}}}}, "/api/integrations/{id}/status": {"patch": {"tags": ["integration-controller"], "operationId": "updateIntegrationStatus", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationStatusRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationDTO"}}}}}}}, "/api/queries/{id}/parameters": {"get": {"tags": ["query-controller"], "operationId": "getQueryParameters", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseListQueryParameterDTO"}}}}}}}, "/api/queries/{id}/history": {"get": {"tags": ["query-controller"], "operationId": "getExecutionHistory", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMapStringObject"}}}}}}}, "/api/queries/favorites": {"get": {"tags": ["query-controller"], "operationId": "getFavorites", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "dataSourceId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMapStringObject"}}}}}}}, "/api/metadata/tables/{id}/columns": {"get": {"tags": ["metadata-controller"], "operationId": "getColumns", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseListColumnDTO"}}}}}}}, "/api/metadata/schemas/{id}/tables": {"get": {"tags": ["metadata-controller"], "operationId": "getTables", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseListTableDTO"}}}}}}}, "/api/metadata/datasources/{id}/schemas": {"get": {"tags": ["metadata-controller"], "operationId": "getSchemas", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseListSchemaDTO"}}}}}}}, "/api/integrations/{id}/preview": {"get": {"tags": ["integration-controller"], "operationId": "previewIntegration", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationQueryResultDTO"}}}}}}}, "/api/datasources/{id}/stats": {"get": {"tags": ["datasource-controller"], "operationId": "getDatasourceStats", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceStatsDTO"}}}}}}}, "/api/datasources/{id}/check-status": {"get": {"tags": ["datasource-controller"], "operationId": "checkDatasourceStatus", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceStatusDTO"}}}}}}}, "/api/v1/llm/configs": {"get": {"tags": ["llm-config-controller"], "operationId": "getAllLLMConfigs", "description": "获取所有LLM服务配置", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseLLMConfigListDTO"}}}}}}, "post": {"tags": ["llm-config-controller"], "operationId": "createLLMConfig", "description": "添加新的LLM服务配置", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LLMConfigCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseLLMConfigDTO"}}}}}}}, "/api/v1/llm/configs/{id}": {"get": {"tags": ["llm-config-controller"], "operationId": "getLLMConfigById", "description": "获取特定LLM服务配置", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseLLMConfigDTO"}}}}}}, "put": {"tags": ["llm-config-controller"], "operationId": "updateLLMConfig", "description": "更新LLM服务配置", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LLMConfigUpdateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseLLMConfigDTO"}}}}}}, "delete": {"tags": ["llm-config-controller"], "operationId": "deleteLLMConfig", "description": "删除LLM服务配置", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseString"}}}}}}}, "/api/v1/llm/configs/{id}/test-connection": {"post": {"tags": ["llm-config-controller"], "operationId": "testLLMConnection", "description": "测试LLM服务连接", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseConnectionTestResult"}}}}}}}, "/api/v1/llm/configs/default/{id}": {"put": {"tags": ["llm-config-controller"], "operationId": "setDefaultLLMConfig", "description": "设置默认LLM服务", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseLLMConfigDTO"}}}}}}}, "/api/v1/llm/stats": {"get": {"tags": ["llm-config-controller"], "operationId": "getLLMStats", "description": "获取LLM服务性能统计", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseLLMStatsDTO"}}}}}}}, "/api/v1/tables/{id}/relations-visualization": {"get": {"tags": ["relation-controller"], "operationId": "getTableRelationVisualization", "description": "获取表关系可视化数据", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "depth", "in": "query", "description": "关系查询深度", "schema": {"type": "integer", "default": 1}}, {"name": "layout", "in": "query", "description": "图形布局算法", "schema": {"type": "string", "enum": ["force", "circular", "hierarchical"], "default": "force"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseRelationVisualizationDTO"}}}}}}}, "/api/v1/relation-visualization/export": {"post": {"tags": ["relation-controller"], "operationId": "exportRelationVisualization", "description": "导出关系图", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelationVisualizationExportDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBinary"}}}}}}}, "/api/v1/page-configs/{id}/versions": {"get": {"tags": ["page-config-controller"], "operationId": "getPageConfigVersions", "description": "获取页面配置历史版本", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponsePageConfigVersionListDTO"}}}}}}}, "/api/v1/page-configs/{id}/diff": {"get": {"tags": ["page-config-controller"], "operationId": "getPageConfigVersionDiff", "description": "获取配置版本间差异", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "from", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "to", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseConfigDiffDTO"}}}}}}}, "/api/v1/page-configs/{id}/rollback/{versionId}": {"post": {"tags": ["page-config-controller"], "operationId": "rollbackPageConfig", "description": "回滚到指定版本", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponsePageConfigDTO"}}}}}}}, "/api/v1/page-configs/{id}/audit-log": {"get": {"tags": ["page-config-controller"], "operationId": "getPageConfigAuditLog", "description": "获取版本操作审计日志", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponsePageConfigAuditLogDTO"}}}}}}}, "/api/v1/performance/slow-queries": {"get": {"tags": ["performance-controller"], "operationId": "getSlowQueries", "description": "获取慢查询列表", "parameters": [{"name": "dataSourceId", "in": "query", "schema": {"type": "string"}}, {"name": "startTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 0}}, {"name": "size", "in": "query", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseSlowQueryPageDTO"}}}}}}}, "/api/v1/performance/slow-queries/{id}": {"get": {"tags": ["performance-controller"], "operationId": "getSlowQueryDetails", "description": "获取慢查询详情", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseSlowQueryDetailDTO"}}}}}}}, "/api/v1/performance/slow-queries/{id}/execution-plan": {"get": {"tags": ["performance-controller"], "operationId": "getQueryExecutionPlan", "description": "获取查询执行计划", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseExecutionPlanDTO"}}}}}}}, "/api/v1/datasources/{id}/sync-diff": {"get": {"tags": ["datasource-controller"], "operationId": "getSyncDiff", "description": "获取元数据同步差异", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "job_id", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseMetadataDiffDTO"}}}}}}}, "/api/v1/datasources/{id}/sync-history/{syncJobId}/diff": {"get": {"tags": ["datasource-controller"], "operationId": "getHistoricalSyncDiff", "description": "获取历史同步差异", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "syncJobId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseMetadataDiffDTO"}}}}}}}, "/api/v1/monitoring/metrics": {"get": {"tags": ["monitoring-controller"], "operationId": "getMetrics", "description": "获取系统指标", "parameters": [{"name": "type", "in": "query", "schema": {"type": "string", "enum": ["system", "query", "datasource", "user"]}}, {"name": "period", "in": "query", "schema": {"type": "string", "enum": ["hour", "day", "week", "month"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseMetricsDTO"}}}}}}}, "/api/v1/monitoring/dashboards": {"get": {"tags": ["monitoring-controller"], "operationId": "getDashboards", "description": "获取可用仪表盘", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseDashboardListDTO"}}}}}}, "post": {"tags": ["monitoring-controller"], "operationId": "createDashboard", "description": "创建自定义仪表盘", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseDashboardDTO"}}}}}}}}, "components": {"schemas": {"ResponseVoid": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "SaveQueryParams": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "dataSourceId": {"type": "string"}, "sql": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "serviceStatus": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "isPublic": {"type": "boolean"}, "queryType": {"type": "string"}}}, "DataSourceInfo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "icon": {"type": "string"}}}, "QueryDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "folderId": {"type": "string"}, "status": {"type": "string"}, "serviceStatus": {"type": "string"}, "dataSourceId": {"type": "string"}, "dataSourceName": {"type": "string"}, "queryType": {"type": "string"}, "queryText": {"type": "string"}, "resultCount": {"type": "integer", "format": "int32"}, "executionTime": {"type": "number"}, "error": {"type": "string"}, "isActive": {"type": "boolean"}, "isFavorite": {"type": "boolean"}, "createdBy": {"$ref": "#/components/schemas/UserInfo"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"$ref": "#/components/schemas/UserInfo"}, "updatedAt": {"type": "string", "format": "date-time"}, "executionCount": {"type": "integer", "format": "int32"}, "lastExecutedAt": {"type": "string", "format": "date-time"}, "tags": {"type": "array", "items": {"type": "string"}}, "currentVersion": {"$ref": "#/components/schemas/QueryVersionDTO"}}}, "QueryParameterDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "label": {"type": "string"}, "defaultValue": {"type": "string"}, "required": {"type": "boolean"}}}, "QueryVersionDTO": {"type": "object", "properties": {"id": {"type": "string"}, "queryId": {"type": "string"}, "versionNumber": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string"}, "sql": {"type": "string"}, "dataSourceId": {"type": "string"}, "dataSource": {"$ref": "#/components/schemas/DataSourceInfo"}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParameterDTO"}}, "status": {"type": "string"}, "isLatest": {"type": "boolean"}, "createdBy": {"$ref": "#/components/schemas/UserInfo"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"$ref": "#/components/schemas/UserInfo"}, "updatedAt": {"type": "string", "format": "date-time"}, "comment": {"type": "string"}}}, "ResponseQueryDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/QueryDTO"}}}, "UserInfo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "avatar": {"type": "string"}}}, "ResponseQueryVersionDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/QueryVersionDTO"}}}, "IntegrationPointInfo": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "urlConfig": {"type": "object"}}}, "UpdateIntegrationRequest": {"required": ["id"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "queryId": {"type": "string"}, "dataSourceId": {"type": "string"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}, "chartConfig": {"type": "object"}}}, "IntegrationDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "queryId": {"type": "string"}, "dataSourceId": {"type": "string"}, "chartConfig": {"type": "object"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointDTO"}, "createdBy": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "IntegrationPointDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "urlConfig": {"type": "object"}}}, "ResponseIntegrationDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/IntegrationDTO"}}}, "UpdateDataSourceRequest": {"required": ["id"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "databaseName": {"type": "string"}, "database": {"type": "string"}, "schema": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "syncFrequency": {"type": "string"}, "connectionParams": {"type": "object"}, "encryptionType": {"type": "string"}, "encryptionOptions": {"type": "object"}}}, "DataSourceDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "databaseName": {"type": "string"}, "database": {"type": "string"}, "schema": {"type": "string"}, "username": {"type": "string"}, "status": {"type": "string"}, "syncFrequency": {"type": "string"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "errorMessage": {"type": "string"}, "connectionParams": {"type": "object"}, "encryptionType": {"type": "string"}, "encryptionOptions": {"type": "object"}, "isActive": {"type": "boolean"}}}, "ResponseDataSourceDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/DataSourceDTO"}}}, "ExecuteQueryParams": {"type": "object", "properties": {"parameters": {"type": "object", "additionalProperties": {"type": "object"}}, "limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}}}, "FieldInfo": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "label": {"type": "string"}}}, "QueryResultDTO": {"type": "object", "properties": {"id": {"type": "string"}, "queryId": {"type": "string"}, "status": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "executionTime": {"type": "number", "format": "double"}, "rowCount": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}, "columns": {"type": "array", "items": {"type": "string"}}, "columnTypes": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldInfo"}}, "error": {"type": "string"}, "warnings": {"type": "array", "items": {"type": "string"}}, "hasMore": {"type": "boolean"}}}, "ResponseQueryResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/QueryResultDTO"}}}, "ResponseBoolean": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "boolean"}}}, "ResponseString": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "string"}}}, "NaturalLanguageQueryParams": {"type": "object", "properties": {"dataSourceId": {"type": "string"}, "question": {"type": "string"}, "contextTables": {"type": "array", "items": {"type": "string"}}, "maxRows": {"type": "integer", "format": "int32"}, "timeout": {"type": "integer", "format": "int32"}}}, "NaturalLanguageToSqlResult": {"type": "object", "properties": {"sql": {"type": "string"}, "explanation": {"type": "string"}, "tables": {"type": "array", "items": {"type": "string"}}}}, "ResponseNaturalLanguageToSqlResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/NaturalLanguageToSqlResult"}}}, "FiltersConfig": {"type": "object", "properties": {"includeSchemas": {"type": "array", "items": {"type": "string"}}, "excludeSchemas": {"type": "array", "items": {"type": "string"}}, "includeTables": {"type": "array", "items": {"type": "string"}}, "excludeTables": {"type": "array", "items": {"type": "string"}}}}, "SyncMetadataRequest": {"type": "object", "properties": {"filters": {"$ref": "#/components/schemas/FiltersConfig"}}}, "ResponseSyncMetadataResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SyncMetadataResponse"}}}, "SyncMetadataResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "syncId": {"type": "string"}, "dataSourceId": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "tablesCount": {"type": "integer", "format": "int32"}, "viewsCount": {"type": "integer", "format": "int32"}, "syncDuration": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "message": {"type": "string"}, "errors": {"type": "array", "items": {"type": "string"}}}}, "CreateIntegrationRequest": {"required": ["dataSourceId", "name", "queryId", "type"], "type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "queryId": {"type": "string"}, "dataSourceId": {"type": "string"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}, "chartConfig": {"type": "object"}}}, "ExecuteIntegrationQueryRequest": {"required": ["integrationId"], "type": "object", "properties": {"integrationId": {"type": "string"}, "parameters": {"type": "object"}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}}, "ColumnDefinition": {"type": "object", "properties": {"field": {"type": "string"}, "label": {"type": "string"}, "type": {"type": "string"}}}, "IntegrationQueryResultDTO": {"type": "object", "properties": {"columns": {"type": "array", "items": {"$ref": "#/components/schemas/ColumnDefinition"}}, "rows": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}, "ResponseIntegrationQueryResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/IntegrationQueryResultDTO"}}}, "CreateDataSourceRequest": {"required": ["databaseName", "host", "name", "password", "port", "syncFrequency", "type", "username"], "type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "databaseName": {"type": "string"}, "database": {"type": "string"}, "schema": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "syncFrequency": {"type": "string"}, "connectionParams": {"type": "object"}, "encryptionType": {"type": "string"}, "encryptionOptions": {"type": "object"}}}, "ResponseTestConnectionResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/TestConnectionResultDTO"}}}, "TestConnectionResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "details": {"type": "object", "additionalProperties": {"type": "object"}}}}, "SyncDataSourceRequest": {"type": "object", "properties": {"includeSchemas": {"type": "array", "items": {"type": "string"}}, "excludeSchemas": {"type": "array", "items": {"type": "string"}}, "includeTables": {"type": "array", "items": {"type": "string"}}, "excludeTables": {"type": "array", "items": {"type": "string"}}}}, "ResponseSyncStatusDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SyncStatusDTO"}}}, "SyncStatusDTO": {"type": "object", "properties": {"syncId": {"type": "string"}, "dataSourceId": {"type": "string"}, "dataSourceName": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "tablesCount": {"type": "integer", "format": "int32"}, "viewsCount": {"type": "integer", "format": "int32"}, "syncDuration": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "progress": {"type": "number", "format": "double"}, "message": {"type": "string"}, "errors": {"type": "array", "items": {"type": "string"}}, "operator": {"type": "string"}, "createdTime": {"type": "string", "format": "date-time"}, "modifiedTime": {"type": "string", "format": "date-time"}}}, "TestConnectionRequest": {"required": ["databaseName", "host", "password", "port", "type", "username"], "type": "object", "properties": {"type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "databaseName": {"type": "string"}, "database": {"type": "string"}, "schema": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "connectionParams": {"type": "object"}, "encryptionType": {"type": "string"}, "encryptionOptions": {"type": "object"}}}, "UpdateIntegrationStatusRequest": {"required": ["status"], "type": "object", "properties": {"status": {"type": "string"}}}, "ResponseMapStringObject": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ResponseListQueryParameterDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParameterDTO"}}}}, "ColumnDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "dataType": {"type": "string"}, "columnType": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "isNullable": {"type": "boolean"}, "isPrimaryKey": {"type": "boolean"}, "isUnique": {"type": "boolean"}, "isIndexed": {"type": "boolean"}, "defaultValue": {"type": "string"}, "characterLength": {"type": "integer", "format": "int32"}, "numericPrecision": {"type": "integer", "format": "int32"}, "numericScale": {"type": "integer", "format": "int32"}, "description": {"type": "string"}}}, "ResponseListColumnDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ColumnDTO"}}}}, "ResponseListTableDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TableDTO"}}}}, "TableDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "rowCount": {"type": "integer", "format": "int64"}, "columnsCount": {"type": "integer", "format": "int32"}}}, "ResponseListSchemaDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaDTO"}}}}, "SchemaDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "tablesCount": {"type": "integer", "format": "int32"}}}, "IntegrationQueryParam": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}}}, "PageResponseIntegrationDTO": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "success": {"type": "boolean"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationDTO"}}, "hasPrevious": {"type": "boolean"}, "hasNext": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}}}, "ResponsePageResponseIntegrationDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageResponseIntegrationDTO"}}}, "DataSourceQueryParam": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}}}, "PageResponseDataSourceDTO": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "success": {"type": "boolean"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DataSourceDTO"}}, "hasPrevious": {"type": "boolean"}, "hasNext": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}}}, "ResponsePageResponseDataSourceDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageResponseDataSourceDTO"}}}, "DataSourceStatsDTO": {"type": "object", "properties": {"dataSourceId": {"type": "string"}, "tablesCount": {"type": "integer", "format": "int32"}, "viewsCount": {"type": "integer", "format": "int32"}, "totalRows": {"type": "integer", "format": "int64"}, "totalSize": {"type": "string"}, "lastUpdate": {"type": "string", "format": "date-time"}, "queriesCount": {"type": "integer", "format": "int32"}, "connectionPoolSize": {"type": "integer", "format": "int32"}, "activeConnections": {"type": "integer", "format": "int32"}, "avgQueryTime": {"type": "string"}, "totalTables": {"type": "integer", "format": "int32"}, "totalViews": {"type": "integer", "format": "int32"}, "totalQueries": {"type": "integer", "format": "int32"}}}, "ResponseDataSourceStatsDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/DataSourceStatsDTO"}}}, "DataSourceStatusDTO": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "isActive": {"type": "boolean"}, "lastCheckedAt": {"type": "string", "format": "date-time"}, "message": {"type": "string"}, "details": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ResponseDataSourceStatusDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/DataSourceStatusDTO"}}}, "ResponseLLMConfigListDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LLMConfigDTO"}}}}, "LLMConfigCreateDTO": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "queryId": {"type": "string"}, "dataSourceId": {"type": "string"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}, "chartConfig": {"type": "object"}}}, "LLMConfigUpdateDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "queryId": {"type": "string"}, "dataSourceId": {"type": "string"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}, "chartConfig": {"type": "object"}}}, "LLMConfigDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "provider": {"type": "string"}, "endpoint": {"type": "string"}, "apiKey": {"type": "string"}, "model": {"type": "string"}, "isDefault": {"type": "boolean"}, "priority": {"type": "integer"}, "timeout": {"type": "integer"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ResponseLLMConfigDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/LLMConfigDTO"}}}, "ResponseLLMStatsDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/LLMStatsDTO"}}}, "LLMStatsDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "queryId": {"type": "string"}, "dataSourceId": {"type": "string"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}, "chartConfig": {"type": "object"}, "status": {"type": "string"}, "isActive": {"type": "boolean"}, "isFavorite": {"type": "boolean"}, "createdBy": {"$ref": "#/components/schemas/UserInfo"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"$ref": "#/components/schemas/UserInfo"}, "updatedAt": {"type": "string", "format": "date-time"}, "executionCount": {"type": "integer", "format": "int32"}, "lastExecutedAt": {"type": "string", "format": "date-time"}, "tags": {"type": "array", "items": {"type": "string"}}, "currentVersion": {"$ref": "#/components/schemas/QueryVersionDTO"}}}, "ResponseConnectionTestResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/ConnectionTestResultDTO"}}}, "ConnectionTestResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "details": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ResponseRelationVisualizationDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/RelationVisualizationDTO"}}}, "RelationVisualizationDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "tables": {"type": "array", "items": {"$ref": "#/components/schemas/TableDTO"}}, "relations": {"type": "array", "items": {"$ref": "#/components/schemas/RelationDTO"}}}}, "RelationDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "sourceTable": {"$ref": "#/components/schemas/TableDTO"}, "targetTable": {"$ref": "#/components/schemas/TableDTO"}, "relationType": {"type": "string"}, "relationStrength": {"type": "number"}}}, "RelationVisualizationExportDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "tables": {"type": "array", "items": {"$ref": "#/components/schemas/TableDTO"}}, "relations": {"type": "array", "items": {"$ref": "#/components/schemas/RelationDTO"}}}}, "ResponseBinary": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "string"}}}, "ResponsePageConfigVersionListDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PageConfigVersionDTO"}}}}, "PageConfigVersionDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ResponseConfigDiffDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/ConfigDiffDTO"}}}, "ConfigDiffDTO": {"type": "object", "properties": {"added": {"type": "object"}, "modified": {"type": "object"}, "deleted": {"type": "object"}}}, "ResponsePageConfigDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageConfigDTO"}}}, "PageConfigDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ResponsePageConfigAuditLogDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PageConfigAuditLogDTO"}}}}, "PageConfigAuditLogDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ResponseSlowQueryPageDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "ResponseSlowQueryDetailDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "ResponseExecutionPlanDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "ResponseMetadataDiffDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "ResponseMetricsDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "ResponseDashboardListDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardDTO"}}}}, "DashboardCreateDTO": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "queryId": {"type": "string"}, "dataSourceId": {"type": "string"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}, "chartConfig": {"type": "object"}}}, "DashboardDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "queryId": {"type": "string"}, "dataSourceId": {"type": "string"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}, "chartConfig": {"type": "object"}}}}}}